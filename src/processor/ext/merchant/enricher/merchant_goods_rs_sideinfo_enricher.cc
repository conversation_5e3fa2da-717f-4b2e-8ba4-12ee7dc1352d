#include "dragon/src/processor/ext/merchant/enricher/merchant_goods_rs_sideinfo_enricher.h"

#include <iostream>
#include <algorithm>
#include <unordered_map>

#include "kconf/kconf.h"
#include "base/thread/thread_pool.h"
#include "base/common/closure.h"
#include "base/common/sleep.h"
#include "base/hash_function/city.h"
#include "learning/kuiba/parameter/parameter.h"
#include "teams/reco-arch/colossus/flat_generated/flat_generated_helper.h"
#include "teams/reco-arch/colossus/client/rpc_client.h"
#include "teams/reco-arch/colossus/client/snack_client.h"
#include "teams/reco-arch/colossus/proto/long_term_service.pb.h"
#include "teams/reco-arch/colossus/proto/common_item.pb.h"
#include "teams/reco-arch/colossus/item/common_item_types.h"

namespace ks {
namespace platform {

bool MerchantGoodsRsSideInfoEnricher::InitProcessor() {
  // click__attrs_name
  clk_item_id_list_attr_ = config()->GetString("good_click_item_id_list_extend",
                                               "good_click_item_id_list_extend");
  clk_timestamp_list_attr_ = config()->GetString("good_click_timestamp_list_extend",
                                                 "good_click_timestamp_list_extend");
  // buy__attrs_name
  buy_item_id_list_attr_ = config()->GetString("good_order_item_id_list_extend",
                                               "good_order_item_id_list_extend");
  buy_timestamp_list_attr_ = config()->GetString("good_order_create_order_time_list_extend",
                                                 "good_order_create_order_time_list_extend");
  // cart__attrs_name
  cart_item_id_list_attr_ = config()->GetString("realtime_cart_iid",
                                                "realtime_cart_iid");

  // realshow__attrs_name
  input_prefix_attr_ = config()->GetString("input_prefix_attr",
                                           "origin_");
  gsu_prefix_attr_ = config()->GetString("gsu_prefix_attr",
                                         "gsu_");
  item_id_list_attr_ = config()->GetString("colossus_rs_item_id_list",
                                           "colossus_rs_item_id_list");
  pageCode_list_attr_ = config()->GetString("colossus_rs_pagecode_id_list",
                                            "colossus_rs_pagecode_id_list");
  timestamp_list_attr_ = config()->GetString("colossus_rs_timestamp_list",
                                             "colossus_rs_timestamp_list");
  spu_id_list_attr_ = config()->GetString("colossus_rs_uniform_spu_id_list",
                                          "colossus_rs_uniform_spu_id_list");
  exposure_ratio_list_attr_ = config()->GetString("colossus_rs_exposure_ratio_list",
                                                  "colossus_rs_exposure_ratio_list");
  //
  seller_id_list_attr_ = config()->GetString("colossus_rs_seller_id_list",
                                             "colossus_rs_seller_id_list");
  category_list_attr_ = config()->GetString("colossus_rs_category_list",
                                            "colossus_rs_category_list");
  //
  cate1_list_attr_ = config()->GetString("colossus_rs_category_1_list",
                                         "colossus_rs_category_1_list");
  cate2_list_attr_ = config()->GetString("colossus_rs_category_2_list",
                                         "colossus_rs_category_2_list");
  cate3_list_attr_ = config()->GetString("colossus_rs_category_3_list",
                                         "colossus_rs_category_3_list");
  lagV1_list_attr_ = config()->GetString("colossus_rs_lagV1_list",
                                         "colossus_rs_lagV1_list");
  is_click_list_attr_ = config()->GetString("colossus_rs_is_click_list",
                                            "colossus_rs_is_click_list");
  is_cart_list_attr_ = config()->GetString("colossus_rs_is_cart_list",
                                           "colossus_rs_is_cart_list");
  is_buy_list_attr_ = config()->GetString("colossus_rs_is_buy_list",
                                          "colossus_rs_is_buy_list");
  count_index_list_attr_ = config()->GetString("colossus_rs_count_index_list",
                                               "colossus_rs_count_index_list");
  lagV2_list_attr_ = config()->GetString("colossus_rs_lagV2_list",
                                         "colossus_rs_lagV2_list");
  ts_count_list_attr_ = config()->GetString("colossus_rs_ts_count_list",
                                            "colossus_rs_ts_count_list");
  id_count_list_attr_ = config()->GetString("colossus_rs_id_count_list",
                                            "colossus_rs_id_count_list");
  is_click_cross_list_attr_ = config()->GetString("colossus_rs_is_click_cross_list",
                                                  "colossus_rs_is_click_cross_list");
  is_buy_cross_list_attr_ = config()->GetString("colossus_rs_is_buy_cross_list",
                                                "colossus_rs_is_buy_cross_list");

  seconds_to_lookback_ = config()->GetInt("seconds_to_lookback_attr", 0);

  // common_attr_limit
  limit_num_ = config()->GetInt("limit_num_attr", 500);

  // gsu config
  gsu_limit_num_ = config()->GetInt("gsu_limit_num_attr", 100);
  cate1_match_attr_ = config()->GetBoolean("cate1_match_attr", true);
  cate2_match_attr_ = config()->GetBoolean("cate2_match_attr", true);
  cate3_match_attr_ = config()->GetBoolean("cate3_match_attr", true);
  use_other_seq_ = config()->GetBoolean("use_other_seq", true);
  cate1_attr_ = config()->GetString("cate1_attr", "iCate1Id");
  cate2_attr_ = config()->GetString("cate2_attr", "iCate2Id");
  cate3_attr_ = config()->GetString("cate3_attr", "iCate3Id");

  return true;
}


int MerchantGoodsRsSideInfoEnricher::GetMinutesLag(int minutes_diff) {
  int minutes_diff_bucket = 0;

  if (minutes_diff < 1) {
      minutes_diff_bucket = 1;
  } else if (minutes_diff < 3) {
      minutes_diff_bucket = 3;
  } else if (minutes_diff < 5) {
      minutes_diff_bucket = 5;
  } else if (minutes_diff < 10) {
      minutes_diff_bucket = 10;
  } else if (minutes_diff <= 60 * 12) {
      minutes_diff_bucket = ((minutes_diff + 15) / 30 + 1) * 30;
  } else if (minutes_diff <= 60 * 24) {
      minutes_diff_bucket = ((minutes_diff + 45) / 90 + 1) * 90;
  } else if (minutes_diff <= 60 * 24 * 7) {
      minutes_diff_bucket = ((minutes_diff + 90) / 180 + 1) * 180;
  } else {
      minutes_diff_bucket = 999999;
  }

  return minutes_diff_bucket;
}


void MerchantGoodsRsSideInfoEnricher::GetCategoryLeval(const std::vector<int64> &cate_list,
                                                      std::vector<int64> *cate1_list,
                                                      std::vector<int64> *cate2_list,
                                                      std::vector<int64> *cate3_list) {
  for (int i = 0; i < cate_list.size(); i++) {
    int64 category = cate_list.at(i);

    cate1_list->emplace_back((category >> 48) & 0xffff);
    cate2_list->emplace_back((category >> 32) & 0xffff);
    cate3_list->emplace_back((category >> 16) & 0xffff);
  }
}


void MerchantGoodsRsSideInfoEnricher::GetLagList(const std::vector<int64> &ts_list,
                                                 int64 request_time,
                                                 std::vector<int64> *lagV1_list,
                                                 std::vector<int64> *lagV2_list) {
  for (int i = 0; i < ts_list.size(); i++) {
    int64 ts = ts_list.at(i);

    int hours_diff = (request_time - ts) / 3600;
    lagV1_list->emplace_back(std::min(static_cast<int>(std::floor(log(hours_diff / 2 + 1.0) * 10)), 200));
    int minutes_diff = (request_time - ts) / 60;
    lagV2_list->emplace_back(GetMinutesLag(minutes_diff));
  }
}


void MerchantGoodsRsSideInfoEnricher::GetCountIndexList(const std::vector<int64> &item_id_list,
                                                        const std::vector<int64> &ts_list,
                                                        std::vector<int64> *count_index_list) {
  std::unordered_map<int64, int> itemid_to_count_dict;
  std::unordered_map<int64, int64> itemid_to_lastTs_dict;
  for (int i = 0; i < item_id_list.size() && i < ts_list.size(); i++) {
    int64 item_id = item_id_list.at(i);
    int64 ts = ts_list.at(i);

    if (itemid_to_count_dict.find(item_id) == itemid_to_count_dict.end()) {
      itemid_to_count_dict[item_id] = 0;
      itemid_to_lastTs_dict[item_id] = -1;
    }

    if (ts != itemid_to_lastTs_dict[item_id]) {
      itemid_to_count_dict[item_id] += 1;
      itemid_to_lastTs_dict[item_id] = ts;
    }

    count_index_list->emplace_back(itemid_to_count_dict[item_id]);
  }
}


void MerchantGoodsRsSideInfoEnricher::GetIsActionList(const std::vector<int64> &item_id_list,
                                      const absl::optional<absl::Span<const int64>> &action_item_id_list,
                                      std::vector<int64> *is_action_list) {
  std::unordered_set<int64> is_action_set;
  if (action_item_id_list && action_item_id_list->size() > 0 && action_item_id_list->size() < 9999999) {
    is_action_set.insert(action_item_id_list->begin(), action_item_id_list->end());
  }
  for (int i = 0; i < item_id_list.size(); i++) {
    int64 item_id = item_id_list.at(i);
    int64 is_action = 1;
    if (is_action_set.find(item_id) == is_action_set.end()) {
      is_action = 0;
    }
    is_action_list->emplace_back(is_action);
  }
}


void MerchantGoodsRsSideInfoEnricher::GetTsCountList(const std::vector<int64> &ts_list,
                                                     std::vector<int64> *ts_count_list) {
  int fast_ind = 0;
  int slow_ind = 0;
  while (fast_ind < ts_list.size()) {
    // generate new fast_ind
    while (fast_ind < ts_list.size() && slow_ind < ts_list.size()
           && ts_list.at(fast_ind) == ts_list.at(slow_ind)) {
      fast_ind = fast_ind + 1;
    }

    // count_diff
    int count_diff = fast_ind - slow_ind;
    if (count_diff < 10) {
      count_diff = count_diff;
    } else if (count_diff < 20) {
      count_diff = count_diff / 2 * 2;
    } else {
      count_diff = std::min(count_diff, 150);
      count_diff = count_diff / 10 * 10;
    }
    // move slow_ind to fast_ind
    while (slow_ind < fast_ind) {
        slow_ind = slow_ind + 1;
        ts_count_list->emplace_back(count_diff);
    }
  }
}


void MerchantGoodsRsSideInfoEnricher::GetIsActionV2List(const std::vector<int64> &realshow_id_list,
                                const std::vector<int64> &realshow_ts_list,
                                const absl::optional<absl::Span<const int64>> &click_id_list,
                                const absl::optional<absl::Span<const int64>> &click_ts_list,
                                std::vector<int64> *is_actionV2_list) {
  if (!click_id_list || click_id_list->size() == 0 || !click_ts_list || click_ts_list->size() == 0
      || click_id_list->size() > 9999999 || click_ts_list->size() > 9999999) {
    for (int rs_index = 0; rs_index < realshow_id_list.size(); rs_index++) {
      is_actionV2_list->emplace_back(0);
    }
    return;
  }

  int clk_fast_index = 0;
  int clk_slow_index = 0;
  std::unordered_map<int64, int> cur_clk_map;
  for (int rs_index = 0; rs_index < realshow_id_list.size(); rs_index++) {
    // move clk_fast_index
    while (clk_fast_index < click_id_list->size() - 1
           && realshow_ts_list.at(rs_index) - click_ts_list->at(clk_fast_index) <= 300) {
      // add_to_map(cur_clk_map, click_id_list[clk_fast_index]);
      int64 clk_fast_iid = click_id_list->at(clk_fast_index);
      if (cur_clk_map.find(clk_fast_iid) == cur_clk_map.end()) {
        cur_clk_map[clk_fast_iid] = 1;
      } else {
        cur_clk_map[clk_fast_iid] += 1;
      }

      clk_fast_index = clk_fast_index + 1;
    }

    // move clk_slow_index
    while (clk_slow_index < click_id_list->size() - 1
            && click_ts_list->at(clk_slow_index) - realshow_ts_list.at(rs_index) > 300) {
      // del_from_map(cur_clk_map, click_id_list[clk_slow_index]);
      int64 clk_slow_iid = click_id_list->at(clk_slow_index);
      if (cur_clk_map.find(clk_slow_iid) == cur_clk_map.end()) {
        cur_clk_map[clk_slow_iid] = 0;
      } else {
        cur_clk_map[clk_slow_iid] -= 1;
      }

      clk_slow_index = clk_slow_index + 1;
    }

    int64 rs_id = realshow_id_list.at(rs_index);
    if (cur_clk_map.find(rs_id) != cur_clk_map.end() && cur_clk_map[rs_id] > 0) {
      is_actionV2_list->emplace_back(cur_clk_map[rs_id]);
    } else {
      is_actionV2_list->emplace_back(0);
    }
  }
}


void MerchantGoodsRsSideInfoEnricher::GetActionCrossList(const std::vector<int64> &is_action_list,
                                                         const std::vector<int64> &is_actionV2_list,
                                                         std::vector<int64> *is_actionCross_list) {
  for (int i = 0; i < is_action_list.size() && i < is_actionV2_list.size(); i++) {
    int64 v2_value = is_actionV2_list[i];
    if (is_actionV2_list[i] > 5) {
      v2_value = 5;
    }
    is_actionCross_list->emplace_back(v2_value + is_action_list[i] * 10);
  }
}


void MerchantGoodsRsSideInfoEnricher::GetCate2RsIndexMap(const std::vector<int64> &cate_list,
                                    std::unordered_map<int64, std::vector<int>> *cate_2_rsIndex) {
  for (int i = 0; i < cate_list.size(); ++i) {
    int64 cate = cate_list[i];
    if (cate_2_rsIndex->find(cate) == cate_2_rsIndex->end()) {
      std::vector<int> init_vec;
      init_vec.push_back(i);
      (*cate_2_rsIndex)[cate] = init_vec;
    } else {
      (*cate_2_rsIndex)[cate].push_back(i);
    }
  }
}


void MerchantGoodsRsSideInfoEnricher::Add2RsIndexSet(std::unordered_set<int> *rsIndex_set,
                                     std::vector<int> *rsIndex_list,
                                     std::unordered_map<int64, int> *rsItemId2Count_map,
                                     const int max_length,
                                     const std::vector<int64> &item_id_list,
                                     const int64 cate,
                                     std::unordered_map<int64, std::vector<int>> *cate_2_rsIndex) {
  if (cate_2_rsIndex->find(cate) != cate_2_rsIndex->end()) {
    auto &cate_vec = (*cate_2_rsIndex)[cate];
    for (int ind = 0; ind < cate_vec.size(); ++ind) {
      if (rsIndex_list->size() >= max_length) {
        break;
      }

      int rs_index  = cate_vec[ind];
      int64 rs_itemId = 0;
      if (rs_index < item_id_list.size()) {
        rs_itemId = item_id_list.at(rs_index);
      }
      if (rsItemId2Count_map->find(rs_itemId) == rsItemId2Count_map->end()) {
        (*rsItemId2Count_map)[rs_itemId] = 1;
        rsIndex_list->push_back(rs_index);
      } else {
        if (rsIndex_set->find(rs_index) == rsIndex_set->end()) {
          (*rsItemId2Count_map)[rs_itemId] += 1;
          if ((*rsItemId2Count_map)[rs_itemId] <= 3) {
            rsIndex_list->push_back(rs_index);
          }
        }
      }

      if (rsIndex_set->find(rs_index) == rsIndex_set->end()) {
        rsIndex_set->insert(rs_index);
      }
    }
  }
}


void MerchantGoodsRsSideInfoEnricher::AssignCommonAttr(MutableRecoContextInterface *context,
                                                       const std::vector<int64> &item_id_list,
                                                       const std::string &attr_name,
                                                       int limit) {
  const auto limit_size = std::min(limit, static_cast<int>(item_id_list.size()));
  std::vector<int64> limit_list(item_id_list.begin(), item_id_list.begin() + limit_size);
  context->SetIntListCommonAttr(attr_name, std::move(limit_list));
}


bool MerchantGoodsRsSideInfoEnricher::IsValidRsList(MutableRecoContextInterface *context,
                              const absl::optional<absl::Span<const int64>> &input_rs_item_id_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_pageCode_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_timestamp_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_spu_id_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_exposure_ratio_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_seller_id_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_category_list) {
  bool is_valid = true;
  if (!input_rs_item_id_list || input_rs_item_id_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_item_id_list is empty";
    is_valid = false;
  }
  if (!input_rs_pageCode_list || input_rs_pageCode_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_pageCode_list is empty";
    is_valid = false;
  }
  if (!input_rs_timestamp_list || input_rs_timestamp_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_timestamp_list is empty";
    is_valid = false;
  }
  if (!input_rs_spu_id_list || input_rs_spu_id_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_spu_id_list is empty";
    is_valid = false;
  }
  if (!input_rs_exposure_ratio_list || input_rs_exposure_ratio_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_exposure_ratio_list is empty";
    is_valid = false;
  }
  if (!input_rs_seller_id_list || input_rs_seller_id_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_seller_id_list is empty";
    is_valid = false;
  }
  if (!input_rs_category_list || input_rs_category_list->size() == 0) {
    CL_LOG(WARNING) << "colossus_rs_category_list is empty";
    is_valid = false;
  }
  if (!is_valid) {
    return is_valid;
  }

  auto rs_item_id_length = input_rs_item_id_list->size();
  auto rs_pageCode_length = input_rs_pageCode_list->size();
  auto rs_timestamp_length = input_rs_timestamp_list->size();
  auto rs_spu_id_length = input_rs_spu_id_list->size();
  auto rs_exposure_ratio_length = input_rs_exposure_ratio_list->size();
  auto rs_seller_id_length = input_rs_seller_id_list->size();
  auto rs_category_length = input_rs_category_list->size();
  if (rs_item_id_length != rs_pageCode_length) {
    CL_LOG(WARNING) << "colossus_rs_pageCode_list`s length is not equal";
    is_valid = false;
  } else if (rs_item_id_length != rs_timestamp_length) {
    CL_LOG(WARNING) << "colossus_rs_timestamp_list`s length is not equal";
    is_valid = false;
  } else if (rs_item_id_length != rs_spu_id_length) {
    CL_LOG(WARNING) << "colossus_rs_spu_id_list`s length is not equal";
    is_valid = false;
  } else if (rs_item_id_length != rs_exposure_ratio_length) {
    CL_LOG(WARNING) << "colossus_rs_exposure_ratio_list`s length is not equal";
    is_valid = false;
  } else if (rs_item_id_length != rs_seller_id_length) {
    CL_LOG(WARNING) << "colossus_rs_seller_id_list`s length is not equal";
    is_valid = false;
  } else if (rs_item_id_length != rs_category_length) {
    CL_LOG(WARNING) << "colossus_rs_category_list`s length is not equal";
    is_valid = false;
  }
  LOG_EVERY_N(INFO, 50000) << "wmx_debug__input_rs_attrs, is_valid:" << is_valid
                       << ", rs_item_id_length:" << rs_item_id_length
                       << ", rs_pageCode_length:" << rs_pageCode_length
                       << ", rs_timestamp_length:" << rs_timestamp_length
                       << ", rs_spu_id_length:" << rs_spu_id_length
                       << ", rs_exposure_ratio_length:" << rs_exposure_ratio_length
                       << ", rs_seller_id_length:" << rs_seller_id_length
                       << ", rs_category_length:" << rs_category_length;


  return is_valid;
}


void MerchantGoodsRsSideInfoEnricher::FilterByTimestamp(MutableRecoContextInterface *context,
                              int64 filter_time,
                              const absl::optional<absl::Span<const int64>> &input_rs_item_id_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_pageCode_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_timestamp_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_spu_id_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_exposure_ratio_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_seller_id_list,
                              const absl::optional<absl::Span<const int64>> &input_rs_category_list,
                              std::vector<int64> *item_id_vec,
                              std::vector<int64> *pageCode_vec,
                              std::vector<int64> *ts_vec,
                              std::vector<int64> *spuId_vec,
                              std::vector<int64> *exposureRatio_vec,
                              std::vector<int64> *sellerId_vec,
                              std::vector<int64> *category_vec) {
  if (!input_rs_item_id_list.has_value()
      || !input_rs_pageCode_list.has_value()
      || !input_rs_timestamp_list.has_value()
      || !input_rs_spu_id_list.has_value()
      || !input_rs_exposure_ratio_list.has_value()
      || !input_rs_seller_id_list.has_value()
      || !input_rs_category_list.has_value()
      ) {
    return;
  }

  auto compare = [&](int a, int b) {
      return input_rs_timestamp_list->at(a) > input_rs_timestamp_list->at(b);
  };
  std::vector<int> indices(input_rs_timestamp_list->size());
  std::iota(indices.begin(), indices.end(), 0);
  std::sort(indices.begin(), indices.end(), compare);

  for (int ind = 0; ind < indices.size(); ind++) {
    int sorted_index = indices[ind];
    int i = sorted_index;

    int64 cur_ts = input_rs_timestamp_list->at(i);
    int64 cur_page_code = input_rs_pageCode_list->at(i);
    // filter_by_ts and filter_by_pageCode
    if (cur_page_code < 1 && cur_page_code > 10 && cur_ts > filter_time) {
      continue;
    }

    item_id_vec->emplace_back(input_rs_item_id_list->at(i));
    pageCode_vec->emplace_back(input_rs_pageCode_list->at(i));
    ts_vec->emplace_back(input_rs_timestamp_list->at(i));
    spuId_vec->emplace_back(input_rs_spu_id_list->at(i));
    exposureRatio_vec->emplace_back(input_rs_exposure_ratio_list->at(i));
    sellerId_vec->emplace_back(input_rs_seller_id_list->at(i));
    category_vec->emplace_back(input_rs_category_list->at(i));
  }
}


void MerchantGoodsRsSideInfoEnricher::Enrich(MutableRecoContextInterface *context,
                                                RecoResultConstIter begin, RecoResultConstIter end) {
  timer_.Start();
  timer_.AppendCostMs("get_colossus_resp");

  thread_local std::vector<int64> clk_item_id_list;
  thread_local std::vector<int64> clk_timestamp_list;
  thread_local std::vector<int64> buy_item_id_list;
  thread_local std::vector<int64> buy_timestamp_list;
  thread_local std::vector<int64> cart_item_id_list;
  // thread_local std::vector<int64> item_id_list;
  // thread_local std::vector<int64> pageCode_list;
  // thread_local std::vector<int64> timestamp_list;
  // thread_local std::vector<int64> spu_id_list;
  // thread_local std::vector<int64> exposure_ratio_list;
  // thread_local std::vector<int64> seller_id_list;
  // thread_local std::vector<int64> category_list;
  thread_local std::vector<int64> cate1_list;
  thread_local std::vector<int64> cate2_list;
  thread_local std::vector<int64> cate3_list;
  thread_local std::vector<int64> lagV1_list;
  thread_local std::vector<int64> is_click_list;
  thread_local std::vector<int64> is_cart_list;
  thread_local std::vector<int64> is_buy_list;
  thread_local std::vector<int64> count_index_list;
  thread_local std::vector<int64> lagV2_list;
  thread_local std::vector<int64> ts_count_list;
  thread_local std::vector<int64> is_click_cross_list;
  thread_local std::vector<int64> is_buy_cross_list;
  thread_local std::vector<int64> is_clickV2_list;
  thread_local std::vector<int64> is_buyV2_list;
  clk_item_id_list.clear();
  clk_timestamp_list.clear();
  buy_item_id_list.clear();
  buy_timestamp_list.clear();
  cart_item_id_list.clear();
  // item_id_list.clear();
  // pageCode_list.clear();
  // timestamp_list.clear();
  // spu_id_list.clear();
  // exposure_ratio_list.clear();
  // seller_id_list.clear();
  // category_list.clear();
  cate1_list.clear();
  cate2_list.clear();
  cate3_list.clear();
  lagV1_list.clear();
  is_click_list.clear();
  is_cart_list.clear();
  is_buy_list.clear();
  count_index_list.clear();
  lagV2_list.clear();
  ts_count_list.clear();
  is_click_cross_list.clear();
  is_buy_cross_list.clear();
  is_clickV2_list.clear();
  is_buyV2_list.clear();

  int64 request_time = context->GetRequestTime() / 1000;
  int64 filter_time = context->GetRequestTime() / 1000 - seconds_to_lookback_;
  auto input_rs_item_id_list = context->GetIntListCommonAttr(input_prefix_attr_ + item_id_list_attr_);
  auto input_rs_pageCode_list = context->GetIntListCommonAttr(input_prefix_attr_ + pageCode_list_attr_);
  auto input_rs_timestamp_list = context->GetIntListCommonAttr(input_prefix_attr_ + timestamp_list_attr_);
  auto input_rs_spu_id_list = context->GetIntListCommonAttr(input_prefix_attr_ + spu_id_list_attr_);
  auto input_rs_exposure_ratio_list = context->GetIntListCommonAttr(input_prefix_attr_
                                                                    + exposure_ratio_list_attr_);
  auto input_rs_seller_id_list = context->GetIntListCommonAttr(input_prefix_attr_ + seller_id_list_attr_);
  auto input_rs_category_list = context->GetIntListCommonAttr(input_prefix_attr_ + category_list_attr_);
  bool is_valid = IsValidRsList(context, input_rs_item_id_list, input_rs_pageCode_list,
                                input_rs_timestamp_list, input_rs_spu_id_list, input_rs_exposure_ratio_list,
                                input_rs_seller_id_list, input_rs_category_list);
  if (!is_valid) {
    CL_LOG(WARNING) << "input_rs_attrs is not valid";
    return;
  }

  std::vector<int64> item_id_vec;
  std::vector<int64> pageCode_vec;
  std::vector<int64> ts_vec;
  std::vector<int64> spuId_vec;
  std::vector<int64> exposureRatio_vec;
  std::vector<int64> sellerId_vec;
  std::vector<int64> category_vec;
  FilterByTimestamp(context, filter_time, input_rs_item_id_list, input_rs_pageCode_list,
                    input_rs_timestamp_list, input_rs_spu_id_list, input_rs_exposure_ratio_list,
                    input_rs_seller_id_list, input_rs_category_list, &item_id_vec, &pageCode_vec,
                    &ts_vec, &spuId_vec, &exposureRatio_vec, &sellerId_vec, &category_vec);

  if (use_other_seq_) {
    auto input_clk_item_id_list = context->GetIntListCommonAttr(clk_item_id_list_attr_);
    auto input_clk_timestamp_list = context->GetIntListCommonAttr(clk_timestamp_list_attr_);
    auto input_buy_item_id_list = context->GetIntListCommonAttr(buy_item_id_list_attr_);
    auto input_buy_timestamp_list = context->GetIntListCommonAttr(buy_timestamp_list_attr_);
    auto input_cart_item_id_list = context->GetIntListCommonAttr(cart_item_id_list_attr_);
    if ((!input_clk_item_id_list && input_clk_timestamp_list) ||
        (input_clk_item_id_list && !input_clk_timestamp_list)) {
      CL_LOG(WARNING) << "one of {input_clk_item_id_list, input_clk_timestamp_list} is nullPtr";
    } else if (input_clk_item_id_list->size() != input_clk_timestamp_list->size()) {
      CL_LOG(WARNING) << "input_clk_item_id_list`s length is not equal with input_clk_timestamp_list"
                      << ", itme_id_length:" << input_clk_item_id_list->size()
                      << ", ts_length:" << input_clk_timestamp_list->size();
    }
    if ((!input_buy_item_id_list && input_buy_timestamp_list) ||
        (input_buy_item_id_list && !input_buy_timestamp_list)) {
      CL_LOG(WARNING) << "one of {input_buy_item_id_list, input_buy_timestamp_list} is nullPtr";
    } else if (input_buy_item_id_list->size() != input_buy_timestamp_list->size()) {
      CL_LOG(WARNING) << "input_buy_item_id_list`s length is not equal with input_buy_timestamp_list"
                      << ", itme_id_length:" << input_buy_item_id_list->size()
                      << ", ts_length:" << input_buy_timestamp_list->size();
    }
  }

  // cate_n_list
  GetCategoryLeval(category_vec, &cate1_list, &cate2_list, &cate3_list);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__cate, cate1_len:" << cate1_list.size()
  //                      << ", cate2_len:" << cate2_list.size()
  //                      << ", cate3_len:" << cate3_list.size();
  // lag_list
  GetLagList(ts_vec, request_time, &lagV1_list, &lagV2_list);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__lag, lag1_len:" << lagV1_list.size()
  //                      << ", lag2_len" << lagV2_list.size();
  // count_index
  GetCountIndexList(item_id_vec, ts_vec, &count_index_list);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__countIndex, length:" << count_index_list.size();
  // ts_count
  GetTsCountList(ts_vec, &ts_count_list);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__tsCount, length:" << ts_count_list.size();
  // is_action_list
  if (use_other_seq_) {
    GetIsActionList(item_id_vec, input_clk_item_id_list, &is_click_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__is_click, length:" << is_click_list.size();
    GetIsActionList(item_id_vec, input_buy_item_id_list, &is_buy_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__is_buy, length:" << is_buy_list.size();
    GetIsActionList(item_id_vec, input_cart_item_id_list, &is_cart_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__is_cart, length:" << is_cart_list.size();
    // is_actionV2_list
    GetIsActionV2List(item_id_vec, ts_vec,
                      input_clk_item_id_list, input_clk_timestamp_list,
                      &is_clickV2_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__is_clickV2, length:" << is_clickV2_list.size();
    GetIsActionV2List(item_id_vec, ts_vec,
                      input_buy_item_id_list, input_buy_timestamp_list,
                      &is_buyV2_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__is_buyV2, length:" << is_buyV2_list.size();
    // is_actionCross_list
    GetActionCrossList(is_click_list, is_clickV2_list, &is_click_cross_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__click_cross, length:" << is_click_cross_list.size();
    GetActionCrossList(is_buy_list, is_buyV2_list, &is_buy_cross_list);
    // LOG_EVERY_N(INFO, 50000) << "wmx_debug__buy_cross, length:" << is_buy_cross_list.size();
  }

  // cate_gsu ------------------------------------------------------------------------------------
  // cate_gsu ------------------------------------------------------------------------------------
  // cate_gsu ------------------------------------------------------------------------------------
  // prepare sta
  thread_local std::unordered_map<int64, std::vector<int>> cate1_2_rsIndex;
  thread_local std::unordered_map<int64, std::vector<int>> cate2_2_rsIndex;
  thread_local std::unordered_map<int64, std::vector<int>> cate3_2_rsIndex;
  cate1_2_rsIndex.clear();
  cate2_2_rsIndex.clear();
  cate3_2_rsIndex.clear();
  GetCate2RsIndexMap(cate1_list, &cate1_2_rsIndex);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__cate1GsuIndexMap, length:" << cate1_2_rsIndex.size();
  GetCate2RsIndexMap(cate2_list, &cate2_2_rsIndex);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__cate2GsuIndexMap, length:" << cate2_2_rsIndex.size();
  GetCate2RsIndexMap(cate3_list, &cate3_2_rsIndex);
  // LOG_EVERY_N(INFO, 50000) << "wmx_debug__cate3GsuIndexMap, length:" << cate3_2_rsIndex.size();

  // prepare init
  thread_local std::vector<int64> gsu_item_id_list;
  thread_local std::vector<int64> gsu_seller_id_list;
  thread_local std::vector<int64> gsu_cate1_list;
  thread_local std::vector<int64> gsu_cate2_list;
  thread_local std::vector<int64> gsu_cate3_list;
  thread_local std::vector<int64> gsu_pageCode_list;
  thread_local std::vector<int64> gsu_spu_id_list;
  thread_local std::vector<int64> gsu_exposure_ratio_list;
  thread_local std::vector<int64> gsu_lagV1_list;
  thread_local std::vector<int64> gsu_lagV2_list;
  thread_local std::vector<int64> gsu_idCount_list;
  thread_local std::vector<int64> gsu_is_click_cross_list;
  thread_local std::vector<int64> gsu_is_buy_cross_list;
  thread_local std::vector<int64> gsu_count_index_list;
  thread_local std::vector<int64> gsu_ts_count_list;
  thread_local std::unordered_set<int> rsIndex_set;
  thread_local std::unordered_map<int64, int> rsItemId2Count_map;
  thread_local std::vector<int> rsIndex_list;
  // generate target_cate1_ids/ target_cate2_ids / target_cate3_ids from reco_result
  auto item_cate1_attr_accessor = context->GetItemAttrAccessor(cate1_attr_);
  auto item_cate2_attr_accessor = context->GetItemAttrAccessor(cate2_attr_);
  auto item_cate3_attr_accessor = context->GetItemAttrAccessor(cate3_attr_);
  for (auto it = begin; it != end; ++it) {
    // init target_item category
    int64 target_cate1 = 0;
    int64 target_cate2 = 0;
    int64 target_cate3 = 0;
    if (context->HasItemAttr(*it, item_cate1_attr_accessor)) {
      auto cate1_id = context->GetIntItemAttr(*it, item_cate1_attr_accessor);
      target_cate1 = (int64_t)(*cate1_id);
    }
    if (context->HasItemAttr(*it, item_cate2_attr_accessor)) {
      auto cate2_id = context->GetIntItemAttr(*it, item_cate2_attr_accessor);
      target_cate2 = (int64_t)(*cate2_id);
    }
    if (context->HasItemAttr(*it, item_cate3_attr_accessor)) {
      auto cate3_id = context->GetIntItemAttr(*it, item_cate3_attr_accessor);
      target_cate3 = (int64_t)(*cate3_id);
    }

    // generate gsu_index
    rsIndex_set.clear();
    rsItemId2Count_map.clear();
    rsIndex_list.clear();
    if (cate3_match_attr_) {
      Add2RsIndexSet(&rsIndex_set, &rsIndex_list, &rsItemId2Count_map, gsu_limit_num_,
                     item_id_vec, target_cate3, &cate3_2_rsIndex);
    }
    // LOG_EVERY_N(INFO, 500000) << "wmx_debug__cate3GsuIndexLength, length:" << rsIndex_list.size();
    if (cate2_match_attr_) {
      Add2RsIndexSet(&rsIndex_set, &rsIndex_list, &rsItemId2Count_map, gsu_limit_num_,
                     item_id_vec, target_cate2, &cate2_2_rsIndex);
    }
    if (cate1_match_attr_) {
      Add2RsIndexSet(&rsIndex_set, &rsIndex_list, &rsItemId2Count_map, gsu_limit_num_,
                     item_id_vec, target_cate1, &cate1_2_rsIndex);
    }

    // generate gsu_attrs
    gsu_item_id_list.clear();
    gsu_seller_id_list.clear();
    gsu_cate1_list.clear();
    gsu_cate2_list.clear();
    gsu_cate3_list.clear();
    gsu_pageCode_list.clear();
    gsu_spu_id_list.clear();
    gsu_exposure_ratio_list.clear();
    gsu_lagV1_list.clear();
    gsu_lagV2_list.clear();
    gsu_idCount_list.clear();
    gsu_is_click_cross_list.clear();
    gsu_is_buy_cross_list.clear();
    gsu_count_index_list.clear();
    gsu_ts_count_list.clear();
    for (int ind = 0; ind < rsIndex_list.size(); ++ind) {
      int cur_rsIndex = rsIndex_list[ind];
      int64 rs_iid = item_id_vec.at(cur_rsIndex);

      gsu_item_id_list.emplace_back(rs_iid);
      gsu_seller_id_list.emplace_back(sellerId_vec.at(cur_rsIndex));
      gsu_cate1_list.emplace_back(cate1_list[cur_rsIndex]);
      gsu_cate2_list.emplace_back(cate2_list[cur_rsIndex]);
      gsu_cate3_list.emplace_back(cate3_list[cur_rsIndex]);
      gsu_pageCode_list.emplace_back(pageCode_vec.at(cur_rsIndex));
      gsu_spu_id_list.emplace_back(spuId_vec.at(cur_rsIndex));
      gsu_exposure_ratio_list.emplace_back(exposureRatio_vec.at(cur_rsIndex));
      gsu_lagV1_list.emplace_back(lagV1_list[cur_rsIndex]);
      gsu_lagV2_list.emplace_back(lagV2_list[cur_rsIndex]);
      gsu_idCount_list.emplace_back(rsItemId2Count_map[rs_iid]);
      if (use_other_seq_) {
        gsu_is_click_cross_list.emplace_back(is_click_cross_list[cur_rsIndex]);
        gsu_is_buy_cross_list.emplace_back(is_buy_cross_list[cur_rsIndex]);
      } else {
        gsu_is_click_cross_list.emplace_back(0);
        gsu_is_buy_cross_list.emplace_back(0);
      }
      gsu_count_index_list.emplace_back(count_index_list[cur_rsIndex]);
      gsu_ts_count_list.emplace_back(ts_count_list[cur_rsIndex]);
    }
    LOG_EVERY_N(INFO, 50000) << "wmx_debug__gsu_last, target_cate3:" << target_cate3
                             << ", target_cate2:" << target_cate2
                             << ", target_cate1:" << target_cate1
                             << ", rsIndex_set.clear_len:" << rsIndex_set.size()
                             << ", rsItemId2Count_map_len:" << rsItemId2Count_map.size()
                             << ", rsIndex_list_len:" << rsIndex_list.size()
                             << ", gsu_iid_num:" << gsu_item_id_list.size();

    if (gsu_item_id_list.size() > 0) {
      uint64 item_key = it->item_key;

      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + item_id_list_attr_,
                                  std::move(gsu_item_id_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + seller_id_list_attr_,
                                  std::move(gsu_seller_id_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + cate1_list_attr_,
                                  std::move(gsu_cate1_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + cate2_list_attr_,
                                  std::move(gsu_cate2_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + cate3_list_attr_,
                                  std::move(gsu_cate3_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + pageCode_list_attr_,
                                  std::move(gsu_pageCode_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + spu_id_list_attr_,
                                  std::move(gsu_spu_id_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + exposure_ratio_list_attr_,
                                  std::move(gsu_exposure_ratio_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + lagV1_list_attr_,
                                  std::move(gsu_lagV1_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + lagV2_list_attr_,
                                  std::move(gsu_lagV2_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + id_count_list_attr_,
                                  std::move(gsu_idCount_list));
      if (use_other_seq_) {
        context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + is_click_cross_list_attr_,
                                    std::move(gsu_is_click_cross_list));
        context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + is_buy_cross_list_attr_,
                                    std::move(gsu_is_buy_cross_list));
      }
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + count_index_list_attr_,
                                  std::move(gsu_count_index_list));
      context->SetIntListItemAttr(item_key, gsu_prefix_attr_ + ts_count_list_attr_,
                                  std::move(gsu_ts_count_list));
    }
  }

  AssignCommonAttr(context, item_id_vec, item_id_list_attr_, limit_num_);
  AssignCommonAttr(context, sellerId_vec, seller_id_list_attr_, limit_num_);
  AssignCommonAttr(context, pageCode_vec, pageCode_list_attr_, limit_num_);
  AssignCommonAttr(context, spuId_vec, spu_id_list_attr_, limit_num_);
  AssignCommonAttr(context, exposureRatio_vec, exposure_ratio_list_attr_, limit_num_);

  AssignCommonAttr(context, cate1_list, cate1_list_attr_, limit_num_);
  AssignCommonAttr(context, cate2_list, cate2_list_attr_, limit_num_);
  AssignCommonAttr(context, cate3_list, cate3_list_attr_, limit_num_);
  AssignCommonAttr(context, lagV1_list, lagV1_list_attr_, limit_num_);
  AssignCommonAttr(context, lagV2_list, lagV2_list_attr_, limit_num_);
  if (use_other_seq_) {
    AssignCommonAttr(context, is_click_cross_list, is_click_cross_list_attr_, limit_num_);
    AssignCommonAttr(context, is_buy_cross_list, is_buy_cross_list_attr_, limit_num_);
  }
  AssignCommonAttr(context, count_index_list, count_index_list_attr_, limit_num_);
  AssignCommonAttr(context, ts_count_list, ts_count_list_attr_, limit_num_);
  if (use_other_seq_) {
    AssignCommonAttr(context, is_click_list, is_click_list_attr_, limit_num_);
    AssignCommonAttr(context, is_cart_list, is_cart_list_attr_, limit_num_);
    AssignCommonAttr(context, is_buy_list, is_buy_list_attr_, limit_num_);
  }

  timer_.AppendCostMs("cluster_search");
}

typedef base::JsonFactoryClass JsonFactoryClass;
FACTORY_REGISTER(JsonFactoryClass, MerchantGoodsRsSideInfoEnricher,
  MerchantGoodsRsSideInfoEnricher);

}  // namespace platform
}  // namespace ks

