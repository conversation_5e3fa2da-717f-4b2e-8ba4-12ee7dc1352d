#!/usr/bin/env python3
# coding=utf-8
"""
filename: merchant_api_mixin.py
description: common_leaf dynamic_json_config DSL intelligent builder, merchant api mixin
author: <EMAIL>
date: 2021-01-14 13:58:00
"""

from ..common_leaf_base_mixin import CommonLeafBaseMixin
from .merchant_retriever import *
from .merchant_enricher import *
from .merchant_arranger import *
from .merchant_leaf_observer import *
from .merchant_mixer import *
from ..common.common_leaf_enricher import CommonRecoDelegateEnricher


class MerchantApiMixin(CommonLeafBaseMixin):
  def full_recall_forward_flow_parser_message_retriever(self, **kwargs):
    """
    FullRecallForwardFlowParserMessageRetriever
    ------
    解析训练产出 embedding message, 召回 item 的同时填充对应 slot 的 embedding, 暂时不支持多 shard
    ------
    `embedding_str_from_common_attr`: [string] 必填 需要解析 pb message 的名称
    `embedding_to_item_attr`: [string] 必填 item embedding 填充到 item attr 的名称
    `embedding_slot_id`: [int] 必填 需要解析 embedding 的 slot id

    调用示例
    ------
    ``` python
    .full_recall_forward_flow_parser_message_retriever(
        btq_topic_name="xxx",
        batch_size=512,
        embedding_to_item_attr="photo_top_layer",
        embedding_slot_id=991,
        retrieve_item_type=0
    )
    ```
    """
    self._add_processor(FullRecallForwardFlowParserMessageRetriever(kwargs))
    return self

  def full_recall_dispatch_uni_score_enricher(self, **kwargs):
    """
    FullRecallDispatchUniScoreEnricher
    ------
    全量打分分数反映射
    ------
    `default_score_value`: [int] 选填项 未索引分数默认值，默认 -999
    `score_from_common_attr`: [string] 必填 获取分数的 common attr 名称
    `score_to_item_attr`: [string] 必填 分发分数的 item attr 名称

    调用示例
    ------
    ``` python
    .full_recall_dispatch_uni_score_enricher(
        score_from_common_attr="paddding_score",
        score_to_item_attr="uni_score" 
    )
    ```
    """
    self._add_processor(FullRecallDispatchUniScoreEnricher(kwargs))
    return self

  def merchant_photo_gsu_good_cate_v2(self, **kwargs):
    """
    MerchantPhotoGsuGoodCateV2
    ------
    对电商视频序列做类目gsu检索
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_good_cate_v2(
      limit_num_attr=50,
      photo_id_list_attr="cart_photo_gsu_pid_list_new",
      author_id_list_attr="cart_photo_gsu_aid_list_new",
      duration_list_attr="cart_photo_gsu_duration_list_new",
      play_time_list_attr="cart_photo_gsu_play_time_list_new",
      play_lag_list_attr="cart_photo_gsu_lag_list_new",
      channel_list_attr="cart_photo_gsu_channel_list_new",
      label_list_attr="cart_photo_gsu_label_list_new",
      index_list_attr="cart_photo_gsu_index_list_new",
      cate1_list_attr="cart_photo_gsu_cate1_list_new",
      cate2_list_attr="cart_photo_gsu_cate2_list_new",
      cate3_list_attr="cart_photo_gsu_cate3_list_new",
      target_cate1_attr='photo_goods_iCate1Id',
      target_cate2_attr='photo_goods_iCate2Id',
      target_cate3_attr='photo_goods_iCate3Id'
      photo_id_list_col= 'photo_id_list_col_',
      author_id_list_col_ = 'author_id_list_col_',
      label_list_col_ = 'label_list_col_',
      time_stamp_col_ = 'time_stamp_col_',
      category_col_ = 'category_col_',
      play_time_list_col_ = 'play_time_list_col_',
      duration_list_col_ = 'duration_list_col_',
      duration_list_col_ = 'duration_list_col_'
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuGoodCateV2Enricher(kwargs))
    return self

  def merchant_mm_score_discrete_enricher(self,**kwargs):
    """
    MerchantMmScoreDiscreteEnricher
    ------
    电商基于 mm embedding 统计的 直方图特征

    参数配置
    ------

    `cart_item_id_list`: [string] 从给定 item attr 获取 小黄车 top3。

    `cart_top_item_score_attr`: [string] 从给定 item attr 获取 小黄车 top3 分数。

    调用示例
    ------
    ``` python
    .merchant_mm_score_discrete_enricher(
      cart_item_id_list='sCartItemTop3List',
      cart_top_item_score_attr='good_click_score_flat',
      output_feature_prefix_attr='mm_histogram'
    )
    ```
    """
    self._add_processor(MerchantMmScoreDiscreteEnricher(kwargs))
    return self

  def attr_label_conv(self,**kwargs):
    """
    AttrLabelConvEnricher
    -----

    参数配置
    -----
    `item_attrs`: [list[string]] 必项, 需要提供转化类型的 item attr 列表.
    调用示例
    ------
    ```Python
    .attr_label_conv(
      attrs=[{}],
      invalid_sample_flag=""
    )
    ```
    """
    self._add_processor(AttrLabelConvEnricher(kwargs))
    return self

  def attr_mio_conv_enrich(self,**kwargs):
    """
    AttrMioConvEnricher
    -----

    参数配置
    -----
    `item_attrs`: [list[string]] 必项, 需要提供转化类型的 item attr 列表.
    调用示例
    ------
    ```Python
    .attr_mio_conv(
      item_attrs=[attr1,attr2]
    )
    ```
    """
    self._add_processor(AttrMioConvEnricher(kwargs))
    return self

  def merchant_infer_enrich(self, **kwargs):
    """
    MerchantInferEnricher
    ------
    调用另一个基于 CommonLeaf 协议的 infer 服务进行计算，并填充返回的属性
    面向在线推理服务调用的性能优化版本, 根据电商业务情况做了算子精简，使用该算子是切勿使用 partition_size 配置分片处理逻辑，
    请选择 internal_partition_size 进行配置
    目前电商侧无分布式推理服务，所以shard_num参数仅支持1，若有分布式推理服务需求，可联系panyuchen、lining17或其他电商架构同学支持。
   
    减配日志：
    1）删除DEFINE_bool(try_packed_item_attr_first, true, "try use packed item attr first")相关逻辑
    2）删除黑名单功能 FLAGS_delegate_enrich_kess_blacklist_kconf
    3) 删除 send_browse_set 功能
    4) 删除 shard by item attr 功能 
    5) 删除 send_item_attrs_in_name_list 功能 （通过一个动态的 name list 去控制）
    6) 删除 item_id_in_attr 功能
    7) 删除根据 ttl 清理 pb 内存功能


    参数配置
    ------
    `kess_service`: [string] [动态参数] 调用的 CommonLeaf 的 kess service

    `kess_cluster`: [string] 选填项，调用 CommonLeaf 的 kess cluster，默认为 PRODUCTION

    `kess_group`: [string] [动态参数] 选填项，调用的 CommonLeaf 的 kess_group，默认为 ""

    `shard_num`: [int] 废弃！！被调服务的 shard 数，该 processor 会并发请求多个 shard，结果合并，默认为 1。

    `shard_id_offset`: [int] 被调服务的 shard_id 起始偏移量，默认为 0，即 shard 号从 s0 开始

    `consistent_hash`: [bool] 是否对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 False

    `hash_id`: [string] [动态参数] 优先使用该 id 对请求进行一致性 hash 的分发，可缺省，缺省时使用 user_id 或 device_id 进行分发， consistent_hash 为 True 时生效

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr，支持对 attr 重命名发送。

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `dynamic_send_item_attrs`: [list] 选填项，发送的 dynamic item attr 列表的 attr 名，默认不发送 dynamic item attr

    `dynamic_send_common_attrs`: [list] 选填项，发送的 dynamic common attr 列表的 attr 名，默认不发送 dynamic common attr

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `recv_item_attrs`: [list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `for_predict`: [bool] 选填项，标记是否是为预估服务请求，若为 true 则会对接收的 double 类型 item_attr 按 pxtr 进行监控上报，默认为 true

    `use_packed_item_attr`: [bool] [动态参数] 选填项，是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，缺省时会优先尝试使用 packed_item_attr 格式，若获取数据失败会取消 packed_item_attr 格式的使用

    `infer_output_type`: [int] 选填项，要求 tower infer server 用指定的 output_type（与服务端 [fetch_tower_remote_pxtr](https://dragonfly.corp.kuaishou.com/#/api/embed_calc?id=fetch_tower_remote_pxtr) 的 output_type 配置功能相同）返回 pxtr 数据，默认为 -1

    `use_sample_list_attr_flag`: [bool] 选填项，是否使用 sample_list 服务获取的 common attrs，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，从指定的 string_list common attr 中获取 sample_list attr 所在的 attr name 列表

    `sample_list_ptr_attr`: [string] 选填项，从指定的 kuiba::PredictItem 类型的 ptr common attr 中获取 sample_list attt, 若与 sample_list_common_attr_key 同时配置，会取并集

    `flatten_sample_list_attr`: [bool] 选填项，是否使用压缩格式发送 sample_list 服务获取的 common attrs，默认为 false

    `flatten_sample_list_attr_to`: [string] 选填项，将 flatten 后的 sample_list attr 以指定的 attr name 发送，仅当 flatten_sample_list_attr=true 时有效，默认为 "kuiba_user_attrs"

    `internal_partition_size`: [int] [动态参数] 选填项，配置算子内部分片的 partition_size, 默认为 50

    `expect_task_num_per_worker`: [int] 选填项，配置组装 request 时每个线程执行的 partition 个数, 默认为 4

    调用示例
    ------
    ``` python
    .merchant_infer_enrich(
        timeout_ms="{{timeout_ms}}",
        kess_service = "kess_service_name",
        request_type="request_type",
        recv_item_attrs=["ctr", "cvr"],
        send_item_attrs=["price"],
        send_common_attrs=["uId"],
        use_packed_item_attr=True,
        internal_partition_size="{{xxx}}",
        expect_task_num_per_worker=4
    )
    ```
    """
    self._add_processor(MerchantInferEnricher(kwargs))
    return self 

  def inc_updater(self, **kwargs):
    """
    IncUpdaterEnricher
    ------
    增量更新模式，不同与Updater双buffer切换的数据更新，从 receiver 接收数据，更新到 uni-predict

    参数配置
    ------
    `trigger_source`: [string] 必配项，更新触发来源，为 "streaming_receiver" 或 "version_based_receiver"

    `key`: [string] 必配项，与 uni-predict processor key 一致

    `bucket_topk_name` 选配项，若使用 `bucket_topk`，需要配置 graph 中 node name

    `snapshot_interval_second`: [int] 选配项，当来源为 "streaming_receiver"，定时更新间隔，默认 180s

    `expire_item_time_second`: [int] 必配项，设定增量cache的item淘汰时间

    示例
    ------
    ``` python
    .inc_updater(trigger_source="streaming_receiver",
             key="test",
             bucket_topk_name="bucket_topk",
             snapshot_interval_second="180")
    .inc_updater(trigger_source="version_based_receiver",
             key="test")
    ```
    """
    self._add_processor(IncUpdaterEnricher(kwargs))
    return self
  
  def full_recall_map_item_id_to_gather_index(self, **kwargs):
    """
    FullRecallMapItemIdToGatherIndexEnricher
    ------
    参数配置
    ------
    `item_id_list_common_attr_name`: [string] 必配项，用于制定生成 gather index 的 item id list 来源，若不填写则使用结果集。

    `gather_index_tensor_common_attr_name`: [string] 必配项，指定生成 gather index tensor 的attr(double list)。
    
    ``` python
    .full_recall_map_item_id_to_gather_index(
      item_id_list_common_attr_name="item_id_list",
      gather_index_tensor_common_attr_name="gather_index"
    )
    ```
    """
    self._add_processor(FullRecallMapItemIdToGatherIndexEnricher(kwargs))
    return self
  
  def uni_predict_full_recall(self, **kwargs):
    """
    UniRecallResultRetriever
    ------
    用于全库召回填充结果.

    参数配置
    ------
    `key`: [string] uni predict 中的 key.
    `topk_index_attr_name`: [string] 存储topk index的attr名字.
    `topk_score_attr_name`: [string] 存储topk index的attr名字.
    `topk_item_id_attr_name`: [string] 输出item id的attr名字.

    调用示例
    ------
    ``` python
    .uni_predict_full_recall(
      key = "test_model",
      topk_score_attr_name = "topk_score",
      topk_index_attr_name = "topk_index"
    )
    ```
    """
    self._add_processor(UniRecallResultRetriever(kwargs))
    return self
  
  def merchant_reco_model_slot_convert_enricher(self, **kwargs):
    """
    MerchantRecoModelSlotConvertEnricher
    ------
    slot id 转化。

    参数
    ------
    `common_slots_from`: [list] 选配项，从哪个 common_attr 中读出 common_slots 列表

    `common_signs_from`: [list] 选配项，从哪个 common_attr 中读出 common_signs 列表

    `item_slots_from`: [list] 选配项，从哪个 item_attr 中读出 item_slots 列表

    `item_signs_from`: [list] 选配项，从哪个 item_attr 中读出 item_signs 列表

    `common_slots_output`: [string] 选配项，将转化的 slot 存入 common_attr 中

    `common_signs_output`: [string] 选配项，将转化的 sign 存入 common_attr 中

    `item_slots_output`: [string] 选配项，将转化的 slot 存入 item_attr 中

    `item_signs_output`: [string] 选配项，将转化的 sign 存入 item_attr 中
    示例
    ------
    ``` python
    .merchant_reco_model_slot_convert_enricher(
        common_slots_from=["common_slots"],
        common_signs_from=["common_signs"],
        item_slots_from=["item_slots"],
        item_signs_from=["item_signs"],
        common_slots_output="common_slots_mio_format",
        common_signs_output="common_signs_mio_format",
        item_slots_output="item_slots_mio_format",
        item_signs_output="item_signs_mio_format",
    ) 
    ```
    """
    self._add_processor(MerchantRecoModelSlotConvertEnricher(kwargs))
    return self
  
  def merchant_convert_live_to_item(self, **kwargs):
    """
    DiffOnlineOfflineEnricher
    ------
    判断线上线下不一致。

    参数
    ------
    `reason`: [int] 召回 reason 字段

    `item_type`: [int] 召回 item_type 字段

    `live_item_key_attr`: [string] 从 retrieval_item_attr 召回 item

    `copy_item_attr_list`: [string_list] 需要拷贝的 live_item_attr
    示例
    ------
    ``` python
    merchant_convert_live_to_item(
      reason=1,
      item_type=99,
      live_item_key_attr="live_item_key",
      copy_item_attr_list=["aGender", "sCartItemList"]
    )
    ```
    """
    self._add_processor(MerchantConvertLiveToItemEnricher(kwargs))
    return self
  
  def merchant_extract_attr_from_common_reco_response_enricher(self, **kwargs):
    """
    MerchantExtractAttrFromCommonRecoResponseEnricher
    ------
    从 common_reco_response 中提取 attr 填充到 common 或者 item 侧

    参数
    ------
    `response_attr`: [string] 必配项，CommonRecoResponse 来源，支持 String 和 PB Message 类型

    `recv_item_attrs`: [list] 选填项，接收的 item_attr 列表，默认不接收 item_attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `recv_return_item_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 return_item_attrs 合并入 recv_item_attrs 发送给下游, 默认为 false
    示例
    ------
    ``` python
    merchant_convert_live_to_item(
      response_attr="response_str",
      recv_item_attrs=["attr1", "attr2"],
      recv_common_attrs=["did"],
      recv_return_item_attrs_in_request=True,
    )
    ```
    """
    self._add_processor(MerchantExtractAttrFromCommonRecoResponseEnricher(kwargs))
    return self

  def merchant_user_meta_info(self, **kwargs):
    """
    CommonRecoUserMetaInfoEnricher
    ------
    将 request 结构中的 user_id, device_id, request_id, request_type 数据作为 common_attr 另存一份，以供其他 Processor 获取处理

    参数配置
    ------
    `save_user_id_to_attr`: [string] 将 user_id 另存到指定的 common_attr 中，缺省则不存

    `save_device_id_to_attr`: [string] 将 device_id 另存到指定的 common_attr 中，缺省则不存

    `save_request_id_to_attr`: [string] 将 request_id 另存到指定的 common_attr 中，缺省则不存

    `save_request_type_to_attr`: [string] 将 request_type 另存到指定的 common_attr 中，缺省则不存

    `save_browse_set_size_to_attr`: [string] 将 browse_set 的 size 另存到指定的 common_attr 中，缺省则不存

    `save_result_size_to_attr`: [string] 将当前结果集的 size 大小另存到指定的 common_attr 中，缺省则不存

    `save_request_time_to_attr`: [string] 将 request_time 另存到指定的 common_attr 中，缺省则不存

    `save_request_num_to_attr`: [string] 将 request_num 另存到指定的 common_attr 中，缺省则不存

    `save_current_time_ms_to_attr`: [string] 将当前时间戳（毫秒数）另存到指定的 common_attr 中，缺省则不存

    `save_host_name_to_attr`: [string] 将当前机器名另存到指定的 common_attr 中，缺省则不存

    `save_host_ip_to_attr`: [string] 将当前机器 ip 另存到指定的 common_attr 中，缺省则不存

    `save_elapsed_time_to_attr`: [string] 将当前请求在服务内部处理已耗费的微秒数存入指定的 common_attr 中，缺省则不存

    `save_shard_no_to_attr`: [string] 将当前服务所在的 shard 号另存到指定的 common_attr 中，缺省则不存

    `save_shard_num_to_attr`: [string] 将当前服务所在的 shard 总数另存到指定的 common_attr 中，缺省则不存

    `save_flow_cpu_cost_to_attr`: [string] 将当前 flow 开始到现在的 cpu 消耗（包括当前 flow 内已经合并回来的 subflow 的 cpu 消耗）存到指定的 common_attr 中，缺省则不存。需开启 gflag pipeline_cpu_cost_sample_rate，采样命中时将该指定的 common_attr 设为大于 0 的 int 值，采样未命中时设为 -1

    `save_need_traceback_to_attr`: [string] 将当前 request 是否被先知采样命中，另存到指定的 common_attr 中，缺省则不存

    调用示例
    ------
    ``` python
    .merchant_user_meta_info(
      save_user_id_to_attr="uid",
      save_device_id_to_attr="did",
    )
    ```
    """
    self._add_processor(MerchantRecoUserMetaInfoEnricher(kwargs))
    return self

  """
  该 Mixin 包含 merchant 相关的 Processor 接口
  - MerchantRedisOrderedIndexRetriever
  - MerchantGetItemAttrFromRedisEnricher
  """
  def write_xtr_score_attr_by_distributed_cache(self, **kwargs):
    """
    MerchantItemXtrWriteDistributeCacheObserver
    -------
    
     参数
    ------
    `kafka_topic`: [string] 同步Kafka队列名称

    `producer_type`: [string_list] 同步类型

    `biz_name`: [string] 业务名称

    `enable_async_queue_write`: [bool] 是否开启异步队列同步
    
    `item_score_attrs`: [string_list] Item Xtr 特征名称

    `extra_attrs`: [list] 选填, 额外需要读取的属性, 目前支持以下 attr。在 extra_attrs 中的 attr 不能出现在 item_score_attrs 中:
     * cache_ttl 表示 item 缓存的过期时间

    调用示例
    ------
    ``` python
    .write_xtr_score_attr_by_distributed_cache(
        select_item = {
        },
        kafka_topic = "",
        producer_type = "kafka",
        biz_name = "",
        enable_async_queue_write = True,
        item_score_attrs = ["pl_ctr", "pl_cvr"],
        extra_attrs = ["cache_ttl"],
    )
    ```
    """
    self._add_processor(MerchantItemXtrWriteDistributeCacheObserver(kwargs))
    return self
  
  def read_xtr_score_attr_by_distributed_cache(self, **kwargs):
    """
    MerchantItemXtrReadDistributedCacheEnricher
    --------

    参数
    ------
    `cache_mode`: [string] 必填，distribute_index 或 redis
    
    `photo_store_kconf_key`: [string]必填 CacheStore配置，使用dynamic_photo_store
    
    `item_score_attrs`: [list] 必填 , ItemXtr特征名称 XtrAttr的值区间应是(0,1)
    
    `cache_hit_flag`: [string] item_attr name，cache命中标记名，命中填1(int)，未命中不填，默认"default_hit_cache"

    `cache_key_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 item_id 参数，优先级最高，配置时`use_user_id_first_key`和`first_key_attr`都不起作用；否则默认使用user_id和item_key的hash结果
    
    `use_user_id_first_key`: [string] 选配项，默认为true，若为false，那么必须设置 `first_key_attr`
    
    `first_key_attr`: [string] 选配项，若设置则使用该 ItemAttr 中的值作为发送请求的 first_key 参数，否则使用 user_id作为 first_key 参数
    
    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    调用示例
    ------
    ``` python
    .read_xtr_score_attr_by_distributed_cache(
      photo_store_kconf_key = "reco.distributedIndex.XXXXX",
      item_score_attrs = [
        "pl_ctr",
        "pl_cvr"
      ],
    )
    ```
    """
    self._add_processor(MerchantItemXtrReadDistributedCacheEnricher(kwargs))
    return self

  def merchant_uni_recall_retrieve_diff(self, **kwargs):
    """
    MerchantUniRecallRetrieveDiff
    --------
    参数
    ------
    `reason_pairs`: [pair]必填 对比reason的pair对, 输出对比reason中的item重复率
    `diff_item_attrs`: [list] 对比相同item下，item属性的对比
    `diff_common_attrs`: [list] 对比common attr
    `default_error_tolerance`: [double] double类型下比较误差，默认1e-4
    
    调用示例
    ------
    ``` python
    .merchant_uni_recall_retrieve_diff(
      reason_pairs=[
        {base: 300001, exp: 300002}, 
        {base: 300003, exp: 300004}
      ],
      diff_item_attrs=[
        "item_id", "item_type", "reason", "score", "item_key"
      ],
      diff_common_attrs=[
        {"base_attr": "attr1", "exp_attr": "attr2"}, 
        {"base_attr": "attr3", "exp_attr": "attr4"}
      ],
      default_error_tolerance=1e-4,
    )
    ```
    """
    self._add_processor(MerchantUniRecallRetrieveDiff(kwargs))
    return self

  def merge_tunnel_with_priority(self, **kwargs):
    """
    MerchantMergeWithPriorityArranger
    --------

    参数
    ------
    `kconf_path`: [string]必填 CacheStore配置，使用dynamic_photo_store
    
    调用示例
    ------
    ``` python
    .merge_tunnel_with_priority(
      kconf_path = "reco.distributedIndex.XXXXX",
    )
    ```
    """
    self._add_processor(MerchantMergeWithPriorityArranger(kwargs))
    return self
  
  def merchant_filter_with_earlystop(self, **kwargs):
    """
    MerchantFilterWithEarlystopArranger
    --------

    参数
    ------
    
    调用示例
    ------
    ``` python
    .merchant_filter_with_earlystop(
      rule_filter_configs=xxx
    )
    ```
    """
    self._add_processor(MerchantFilterWithEarlystopArranger(kwargs))
    return self

  def merchant_parse_candidate_from_cache_context_enricher(self, **kwargs):
    """
    MerchantParseCandidateFromCacheContextEnricher
    """
    self._add_processor(MerchantParseCandidateFromCacheContextEnricher(kwargs))
    return self
  
  def merchant_dump_attr_to_cache_context_enricher(self, **kwargs):
    """
    MerchantDumpAttrToCacheContextEnricher
    ------

    参数
    ------
    `parse_from_attr`: [string] parse_from_attr

    `item_attrs`: [string_list] item_attrs

    `dump_to_attr`: [string] dump_to_attr

    `cache_expire_time_attr`: [string] cache_expire_time_attr

    `dump_prefix_attr`: [string] dump_prefix_attr

    调用示例
    ------
    ``` python
    .merchant_dump_attr_to_cache_context_enricher(
      parse_from_attr="get_full_rank_retrieval_score_list_context",
      item_attrs=["sc_live_ctr"],
      dump_to_attr="save_full_rank_retrieval_score_list_context",
      cache_expire_time_attr="dump_expire_time",
      dump_prefix_attr="ffr_"
    )
    ```
    """
    self._add_processor(MerchantDumpAttrToCacheContextEnricher(kwargs))
    return self

  def merchant_parse_attr_from_cache_context_enricher(self, **kwargs):
    """
    MerchantParseAttrFromCacheContextEnricher
    ------

    参数
    ------
    `parse_from_attr`: [string] parse_from_attr

    `extract_item_attrs`: [string_list] extract_item_attrs

    `cache_valid_time_attr`: [string] cache_valid_time_attr

    `parse_prefix_attr`: [string] parse_prefix_attr

    调用示例
    ------
    ``` python
    .merchant_parse_attr_from_cache_context_enricher(
      parse_from_attr="get_full_rank_retrieval_score_list_context",
      extract_item_attrs=["sc_live_ctr"],
      cache_valid_time_attr="parse_valid_time",
      parse_prefix_attr="ffr_"
    )
    ```
    """
    self._add_processor(MerchantParseAttrFromCacheContextEnricher(kwargs))
    return self 

  def merchant_user_click_item_action_cart_gsu_opt_enricher(self, **kwargs):
    """
    MerchantUgClickItemGsuEnricher
    """
    self._add_processor(MerchantUserClickItemActionCartGsuOptEnricher(kwargs))
    return self 
  
  def merchant_ug_live_item_gsu_enricher(self, **kwargs):
    """
    MerchantUgLiveItemGsuEnricher
    """
    self._add_processor(MerchantUgLiveItemGsuEnricher(kwargs))
    return self

  def merchant_ug_click_item_gsu_enricher(self, **kwargs):
    """
    MerchantUgClickItemGsuEnricher
    """
    self._add_processor(MerchantUgClickItemGsuEnricher(kwargs))
    return self

  def merchant_ug_order_item_gsu_enricher(self, **kwargs):
    """
    MerchantUgOrderItemGsuEnricher
    """
    self._add_processor(MerchantUgOrderItemGsuEnricher(kwargs))
    return self
  
  def merchant_ug_click_item_gsu_opt_enricher(self, **kwargs):
    """
    MerchantUgClickItemGsuOptEnricher
    """
    self._add_processor(MerchantUgClickItemGsuOptEnricher(kwargs))
    return self 
  
  def merchant_ug_order_item_gsu_opt_enricher(self, **kwargs):
    """
    MerchantUgOrderItemGsuOptEnricher
    """
    self._add_processor(MerchantUgOrderItemGsuOptEnricher(kwargs))
    return self

  def retrieve_by_merchant_redis_ordered_index(self, **kwargs):
    """
    MerchantRedisOrderedIndexRetriever
    ------
    从 redis 有序索引中召回 item

    参数配置
    ------
    `cluster_name`: [string] redis 服务的服务名

    `timeout_ms`: [int] 单次请求 redis 的超时时间, 默认值为 100

    `min_score`: [int] [动态参数] 从 redis 服务中获取的分数范围的最小值

    `max_score`: [int] [动态参数] 从 redis 服务中获取的分数范围的最大值, 注意 max_score 必须比 min_score 大, 最后吐出的 item 顺序是从大到小排序

    `prefix_name`: [string] [动态参数] 获取 redis 的前缀, 默认空

    `suffix_name`: [string] [动态参数] 获取 redis 的后缀, 默认空

    `key_list_attr_type`: [string] 默认的 key_list 数据类型, 支持 int, double, string, 默认为 int

    `key_list`: [动态参数] 获取 redis 的keyList, 类型同 key_list_attr_type, 会和前后缀组合成为若干个 key

    `num`: [int] 单个 key 最多召回个数

    `max_num`: [int] 全量 key 最多召回个数

    `batch_num`: [int] 最大的并发同时请求 redis 的数量, 默认 100

    `key_save_attr` :[int] 该 item 被什么 key 召回, 会写入到该字段中

    `reason`: [int] 召回原因

    调用示例
    ------
    ``` python
    .retrieve_by_merchant_redis_ordered_index(
      cluster_name = "clusterXXX",
      timeout_ms = 200,
      min_score = 10,
      max_score = 200,
      prefix_name = "AAA",
      suffix_name = "BBB",
      key_list = ["test1", "test2"],
      num = 100,
      max_num = 300,
      key_save_attr = "redis_recall",
      reason = 999
    )
    ```
    """
    self._add_processor(MerchantRedisOrderedIndexRetriever(kwargs))
    return self
  
  def merchant_good_click_retriever_with_colossus_resp(self, **kwargs):
    """
    MerchantGoodClickColossusRespRetriever
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_item_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_cate1_to_attr`: [string]

    `save_cate2_to_attr`: [string]

    `save_cate3_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    调用示例
    ------
    ``` python
    .merchant_good_click_retriever_with_colossus_resp(
      colossus_resp_attr="colossus_resp_click_good_item",
      save_item_id_to_attr="item_click_item_id",
      save_author_id_to_attr="item_click_author_id",
      save_cate1_to_attr="item_click_item_cate1",
      save_cate2_to_attr="item_click_item_cate2",
      save_cate3_to_attr="item_click_item_cate3",
      save_timestamp_to_attr="item_click_time_stamp",
      filter_future_attr=True)
    ```
    """
    self._add_processor(MerchantGoodClickColossusRespRetriever(kwargs))
    return self
 
  def merchant_good_click_retriever_with_colossus_resp_v2(self, **kwargs):
    """
    MerchantGoodClickColossusRespRetrieverV2
    ------

    参数
    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    The following are required attrs from the resp_attr, could be empty
    ------
    `save_item_id_to_attr`: [string]

    `save_author_id_to_attr`: [string]

    `save_cate1_to_attr`: [string]

    `save_cate2_to_attr`: [string]

    `save_cate3_to_attr`: [string]

    `save_timestamp_to_attr`: [string]

    `save_real_price_to_attr`: [string]

    `save_origin_price_to_attr`: [string]
    
    `save_label_to_attr`: [string]
    
    `save_seller_id_to_attr`: [string]
    
    `save_real_seller_id_to_attr`: [string]
    
    `save_click_flow_type_to_attr`: [string]

    `save_detail_page_view_time_to_attr`: [string]

    `save_leaf_cate_to_attr`: [string]

    `filter_duplicate_attr` : [boolean] 是否只取重复点击的colossus pid, 默认为false, 是的话取第一个点击

    `filter_future_attr` : [boolean] 是否只取时间在request time之前的colossus pid, 默认为false

    调用示例
    ------
    ``` python
    .merchant_good_click_retriever_with_colossus_resp_v2(
      colossus_resp_attr="colossus_resp_click_good_item",
      save_item_id_to_attr="item_click_item_id",
      save_click_from_to_attr="item_click_from_pid",
      save_cate1_to_attr="item_click_item_cate1",
      save_cate2_to_attr="item_click_item_cate2",
      save_cate3_to_attr="item_click_item_cate3",
      save_timestamp_to_attr="item_click_time_stamp",
      save_real_price_to_attr="item_click_real_price",
      save_origin_price_to_attr="item_click_origin_price",
      save_label_to_attr="item_click_label",
      save_seller_id_to_attr="item_click_seller_id",
      save_real_seller_id_to_attr="item_click_real_seller_id",
      save_click_flow_type_to_attr="item_click_click_flow_type",
      filter_duplicate_attr=True,
      filter_future_attr=True)
    ```
    """
    self._add_processor(MerchantGoodClickColossusRespRetrieverV2(kwargs))
    return self

  def merchant_retrieve_by_local_kv_index(self, **kwargs):
    """
    MerchantRecoLocalKvIndexRetriever
    ------
    从本地 attr index 固定格式召回
    
    参数
    ------
    `table_name`: [string] 本地索引表名, 与 attr_index_kconf_path 中的配置对应

    `attr_index_kconf_path`: [string] 本地索引的 kconf 配置路径, 若无 table_name 关联的配置，则初始化失败

    `key`: [string] 指定一个固定的索引 key (注意：`key` 与 `key_from_attr` 需至少配置一项)

    `key_from_attr`: [string] 从指定的 common_attr 中获取动态的主键 key，主键支持 int/string/int_list/string_list 类型，int 类型会先转换为 uint64 再转换成 string (注意：`key` 与 `key_from_attr` 需至少配置一项)
    
    `key_prefix`: [string] [动态参数] 选配项，为 `key_from_attr` 中每个 key 的值添加一个前缀，默认为空

    `reason`: [int] 召回原因

    `reason_from_attr`: [int] 选配项，当配置了 `key_from_attr` 的情况下，可通过该项配置从某个 int_list 类型的 CommonAttr 中获取 reason 列表，来给 `key_from_attr` 中的每个 key 分配不同的 reason 值。两个 list 长度未对齐的部分将回退使用 `reason` 配置的值。

    `retrieval_item_type`: [int] 召回物料类型；商品：99、直播：1、短视频：0

    `retrieve_item_id_attr`: [int] 从索引中获取item_id_list的列名，默认为 recall_item_ids。item_id_list类型为int_list。

    `retrieve_item_score_attr`: [int] 从索引中获取item_score_list的列名，默认为 recall_scores。item_score_list类型为double_list，如果list长度与召回item_id长度不相等，则废弃score分数，默认为0。

    `retrieve_num`: [int] [动态参数] 必配项，召回结果的数量上限

    `retrieve_num_per_key`: [int] [动态参数] 选配项，单个 key 召回的数量上限，默认为 10000

    `extra_item_attrs`: [list] 选配项，item 需要额外填充的 item_attrs 列表，类型为list类型，长度必须与item_list相等
      - `name`: [string] 索引查询列名 item_attr 的名称，读出来的值必须为list类型 int_list/double_list/string_list
      - `as_name`: [string] 存入 item_attr 的别名，默认为 name

    
    调用示例
    ------
    ``` python
    .merchant_retrieve_by_local_kv_index(
      table_name="test_table",
      attr_index_kconf_path="reco.distributedIndex.uni_recall_local_index",
      reason=100,
      key="hot_recall",
      retrieve_item_id_attr = "recall_item_ids",
      retrieve_item_score_attr = "recall_scores",
      extra_item_attrs=[
          {"name": "hot_score", "as_name": "100_hot_score"},
      ],
    )
    ```
    """
    self._add_processor(MerchantRecoLocalKvIndexRetriever(kwargs))
    return self

  def merchant_uni_recall_local_kv_trigger(self, **kwargs):
    """
    MerchantRecoLocalKvTriggerRetriever
    ------
    电商统一召回KV-Trigger，通过kconf配置管理kv类召回

    参数
    ------
    `kv_triger_kconf_path`: [string] kv类召回配置kconf路径 「主配置和灰度配置必须有一个」

    `kv_triger_kconf_gray_path`: [string] 测试配置，可自定义名字，覆盖主配置生效「主配置和灰度配置必须有一个」

    `attr_index_kconf_path`: [string] 本地索引的 kconf 配置路径

    调用示例
    ------
    ``` python
    .merchant_uni_recall_local_kv_trigger(
      kv_triger_kconf_path="reco.distributedIndex.uni_recall_kv_trigger",
      attr_index_kconf_path="reco.distributedIndex.uni_recall_local_index",
    )
    ```
    """
    self._add_processor(MerchantRecoLocalKvTriggerRetriever(kwargs))
    return self

  def merchant_uni_recall_direct_trigger(self, **kwargs):
    """
    MerchantRecoCommonAttrRetriever
    ------
    电商统一召回Direct-Trigger，通过kconf配置管理透传类召回

    参数
    ------
    `direct_triger_kconf_path`: [string] 透传类召回配置kconf路径 「主配置和灰度配置必须有一个」

    `direct_triger_kconf_gray_path`: [string] 测试配置，可自定义名字，覆盖主配置生效「主配置和灰度配置必须有一个」

    调用示例
    ------
    ``` python
    .merchant_uni_recall_direct_trigger(
      direct_triger_kconf_path="reco.distributedIndex.uni_recall_direct_trigger",
    )
    ```
    """
    self._add_processor(MerchantRecoCommonAttrRetriever(kwargs))
    return self

  def live_gsu_session(self, **kwargs):
    """
    MerchantGsuSession
    ------
    user live session

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `session_time_range_attr`: [int] session窗口attr

    `n_minute_ago`: [int] n_minute_ago 避免训练线上线下不一致, 写入colossus延时时间
 
    `filter_show_attr`: [boolean] 是否过滤掉间外播放,默认为false
    调用示例
    ------
    ``` python
    .live_gsu_session(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      session_time_range_attr='session_time_range_attr_num',
                      n_minute_ago=1,
                      filter_show_attr=True,
                      slots_id=[7750,7751,7752,7753,7754,7755,7756,7757,7758,7759,7760,7761,7762,7763,7764],
                      mio_slots_id=[7750,7751,7752,7753,7754,7755,7756,7757,7758,7759,7760,7761,7762,7763,7764]
                      )
    ```
    """
    self._add_processor(MerchantGsuSession(kwargs))
    return self

  def live_gsu_multi_session(self, **kwargs):
    """
    MerchantGsuMultiSessionEnricher
    ------
    user live multi session

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `session_time_range_attr`: [int] session窗口attr

    `n_minute_ago`: [int] n_minute_ago 避免训练线上线下不一致, 写入colossus延时时间
 
    `filter_show_attr`: [boolean] 是否过滤掉间外播放,默认为false
    调用示例
    ------
    ``` python
    .live_gsu_multi_session(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      session_time_range_attr='session_time_range_attr_num',
                      n_minute_ago=1,
                      filter_show_attr=True,
                      slots_id=[7750,7751,7752,7753,7754,7755,7756,7757,7758,7759,7760,7761,7762,7763,7764],
                      mio_slots_id=[7750,7751,7752,7753,7754,7755,7756,7757,7758,7759,7760,7761,7762,7763,7764]
                      )
    ```
    """
    self._add_processor(MerchantGsuMultiSessionEnricher(kwargs))
    return self

  def live_gsu_all_multi_session(self, **kwargs):
    """
    MerchantGsuMultiSessionEnricher
    ------
    user live multi session

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `session_time_range_attr`: [int] session窗口attr

    `n_minute_ago`: [int] n_minute_ago 避免训练线上线下不一致, 写入colossus延时时间
 
    `filter_show_attr`: [boolean] 是否过滤掉间外播放,默认为false
    调用示例
    ------
    ``` python
    .live_gsu_all_multi_session(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      limit_num_attr='live_limit_num',
                      session_time_range_attr='session_time_range_attr_num',
                      n_minute_ago=1,
                      filter_show_attr=True,
                      slots_id=[7750,7751,7752,7753,7754,7755,7756,7757,7758,7759,7760,7761,7762,7763,7764],
                      mio_slots_id=[7750,7751,7752,7753,7754,7755,7756,7757,7758,7759,7760,7761,7762,7763,7764]
                      )
    ```
    """
    self._add_processor(MerchantGsuAllMultiSessionEnricher(kwargs))
    return self

  def merchant_extract_live_cart(self, **kwargs):
    """
    MerchantExtractLiveCart 
    ------
    extrcat live cart
    参数
    ------
    `sCartItemList_attr` : [string] 直播间item
    
    `sCartItemCate1IdList_attr` : [string] 直播间类目

    `sCartItemCate2IdList_attr` : [string] 直播间类目

    `sCartItemCate3IdList_attr` : [string] 直播间类目

    `sCartItemCategoryLeafIdList_attr` : [string] 直播间叶子类目

    `sCartItemGoodPayOrder7DaysList_attr` : [string] 直播间top下单

    `sCartItemGoodPayGmv7DaysList_attr` : [string] 直播间top GMV

    示例
    ------
    ``` python
    .merchant_extract_live_cart(
        sCartItemList_attr="sCartItemList",
        sCartItemCate1IdList_attr="sCartItemCate1IdList",
        sCartItemCate2IdList_attr="sCartItemCate2IdList",
        sCartItemCate3IdList_attr="sCartItemCate3IdList",
        sCartItemCategoryLeafIdList_attr="sCartItemCategoryLeafIdList",
        sCartItemGoodPayOrder7DaysList_attr="sCartItemGoodPayOrder7DaysList",
        sCartItemGoodPayGmv7DaysList_attr="sCartItemGoodPayGmv7DaysList"
    )
    ```
    """
    self._add_processor(MerchantExtractLiveCartEnricher(kwargs))
    return self

  def merchant_extract_live_realfea(self, **kwargs):
    """
    MerchantExtractLiveRealfea
    ------
    extrcat live realfea
    参数
    ------
    示例
    ------
    ``` python
    .merchant_extract_live_realfea(
      LiveRealshowCountSlide1m_attr="reco.feart_live_count_feature.LiveRealshowCountSlide1m",
      LiveRealshowCountSlide5m_attr="reco.feart_live_count_feature.LiveRealshowCountSlide5m",
      LiveRealshowCountSlide10m_attr="reco.feart_live_count_feature.LiveRealshowCountSlide10m",
      LiveClickCountSlide1m_attr="reco.feart_live_count_feature.LiveClickCountSlide1m",
      LiveClickCountSlide5m_attr="reco.feart_live_count_feature.LiveClickCountSlide5m",
      LiveClickCountSlide10m_attr="reco.feart_live_count_feature.LiveClickCountSlide10m",
      LiveClickCountAll1m_attr="reco.feart_live_count_feature.LiveClickCountAll1m",
      LiveClickCountAll5m_attr="reco.feart_live_count_feature.LiveClickCountAll5m",
      LiveClickCountAll10m_attr="reco.feart_live_count_feature.LiveClickCountAll10m",
      PayCount1m_attr="reco.feart_live_count_feature.PayCount1m",
      PayCount5m_attr="reco.feart_live_count_feature.PayCount5m",
      PayCount10m_attr="reco.feart_live_count_feature.PayCount10m",
      PayAmount1m_attr="reco.feart_live_count_feature.PayAmount1m",
      PayAmount5m_attr="reco.feart_live_count_feature.PayAmount5m",
      PayAmount10m_attr="reco.feart_live_count_feature.PayAmount10m"
    )
    ```
    """
    self._add_processor(MerchantExtractLiveRealfeaEnricher(kwargs))
    return self

  def gsu_with_index_good_click(self, **kwargs):
    """
    GsuWithIndexGoodClickEnricher
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `cate1_attr` : [string] pid对应的一级类目 attr name

    `cate2_attr` : [string] pid对应的二级类目 time attr name

    `cate3_attr` : [string] pid对应的三级类目 attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `colossus_result_as_list`: [boolean], 假如上游 CommonRecoColossusRespRetriever 已经把 photo_id, author_id, duration, play_time, tag, label, timestamp
                               作为一个 list 一起写到 item attr 中, 这里可以开启这个开关，将其一次做为一个 list 读出来; colossus_result_as_list 为 true 的时候
                               必须提供 `colossus_result_list_attr`，这时 author_id_attr, duration_attr，play_time_attr，tag_attr，timestamp_attr 不再生效;
                               这样做是起到一些性能优化的作用

    `colossus_result_list_attr`: [string], 将 author_id_attr，duration_attr，play_time_attr，tag_attr，timestamp_attr 做为一个 list 从 item 侧属性读出来

    调用示例
    ------
    ``` python
    .gsu_with_index_good_click(sorted_item_idx_attr="distance",
                    matrixcolossus_pid_attr="colossus_pid",
                    author_id_attr="aid_attr",
                    tag_attr="tag_attr",
                    play_time_attr="play_time_attr",
                    duration_attr="duration_attr",
                    timestamp_attr="ts_attr",
                    output_sign_attr="output_sign",
                    output_slot_attr="output_slot",
                    top_n=50)
    ```
    """
    self._add_processor(GsuWithIndexGoodClickEnricher(kwargs))
    return self
  
  def fetch_merchant_item_attr_from_redis(self, **kwargs):
    """
    MerchantGetItemAttrFromRedisEnricher
    ------
    从 redis 里面获取itemAttr, 会用前缀拼上 itemId 拼上后缀, 组成 key 查询 redis 获取value

    参数配置
    ------
    `cluster_name`: [string] redis 服务的服务名

    `timeout_ms`: [int] 单次请求 redis 的超时时间, 默认值为 100

    `prefix_name`: [string] [动态参数] 获取 redis 的前缀, 默认 空

    `suffix_name`: [string] [动态参数] 获取 redis 的后缀, 默认 空

    `batch_num`: [int] 最大的并发调用数量, 默认 5

    `attr_type`: [string] 默认的数据类型, 支持 int, double, string

    `default_value`: 获取失败或者为空的默认值, 类型同 attr_type 一致

    `save_item_attr`: [string] 存入的 itemAttr 字段名

    调用示例
    ------
    ``` python
    .fetch_merchant_item_attr_from_redis(
      cluster_name = "clusterXXX",
      timeout_ms = 200,
      prefix_name = "AAA",
      suffix_name = "BBB",
      attr_type = "int",
      default_value = 0,
      save_item_attr = "xxxxxx"
    )
    ```
    """
    self._add_processor(MerchantGetItemAttrFromRedisEnricher(kwargs))
    return self
  
  def change_item_to_common(self, **kwargs):
    """
    ShopCustomerChangeItemToCommonEnricher
    ------
    将 item 转化为电商客服的 common attr，用于后续的 retriever 调用

    参数配置
    ------
    `attr_name`: [string] 必填，最后存储到的 common_attr 字段名字 attr_name 

    `intent`: [string] 必填，intent 的 item_attr 名字，表示问题的相似问

    `question`: [string] 必填，question 的 item_attr 名字，表示问题的标准问

    `answer`: [string] 必填，answer 的 item_attr 名字，表示问题的回答

    `intent_id`: [string] intent_id 的字段名，默认为 intent_id

    `answer_id`: [string] answer_id 的字段名，默认为 answer_id

    `question_id`: [string] question_id 的字段名，默认为 question_id

    `knowledge_id`: [string] knowledge_id 的字段名，默认为 knowledge_id
    
    调用示例
    ------
    ``` python
    .change_item_to_common(
      attr_name="changeItem",
      intent="intent",
      question="question",
      answer="answer",
    )
    ```
    """
    self._add_processor(ShopCustomerChangeItemToCommonEnricher(kwargs))
    return self

  def intent_deduplicate(self, **kwargs):
    """
    ShopCustomerDeduplicateEnricher
    ------
    将 item 转化为电商客服的 common attr，用于后续的 retriever 调用

    参数配置
    ------
    `output_dedpulicate_attr`: [string] 必填，最后存储到的 common_attr 字段名字 attr_name 

    `intent_id`: [string] intent_id 的字段名，默认为 intent_id

    `intent`: [string] intent 的字段名，默认为 intent

    `tenant_id`: [string] tenant_id 的字段名，默认为 tenant_id

    `scene_id`: [string] tenant_id 的字段名，默认为 scene_id
    
    调用示例
    ------
    ``` python
    .intent_deduplicate(
      output_dedpulicate_attr="is_intent_depuplicated",
      intent="intent",
      tenant_id="tenant_id",
      scene_id="scene_id",
    )
    ```
    """
    self._add_processor(ShopCustomerDeduplicateEnricher(kwargs))
    return self

  def retrieve_answer_type_from_faq_service(self, **kwargs):
    """
    ShopCustomerFaqAnswerTypeRetriever
    ------
    访问电商客服 Faq 服务，将获取 answer_type，会将得到的结果存储在对应的 item_attr 中

    参数配置
    ------
    `kess_service`: [string] 必填，faq server 的 kess name

    `timeout_ms`: [int] 单次请求 grpc_faq_service 的超时时间, 默认值为 200

    `from_name`: [string] 从 from_name 的 common_attr 字段解析 item

    `query`: [string] [动态参数] 必填，request 携带的 query

    `intent`: [string] 必填，intent 的 item_attr 名字，表示问题的相似问

    `question`: [string] 必填，question 的 item_attr 名字，表示问题的标准问

    `answer`: [string] 必填，answer 的 item_attr 名字，表示问题的回答

    `answer_type`: [string] 必填，最终的处理结果存储的 item_attr 的名字，最终结果有三种：1. 唯一回答（only_answer)，2. 列表回答(list_answer)，3. 兜底回答(last_answer)。 最后结果是 int 类型的 item_attr，其数值会用 1，2，3 来分别进行返回
  
    `get_id`: [int] 是否要获取 id 类的特征，1为需要，0为不需要，默认1。

    `intent_id`: [string] intent_id 最终填写的字段名，默认为 intent_id

    `answer_id`: [string] answer_id 最终填写的字段名，默认为 answer_id

    `question_id`: [string] question_id 最终填写的字段名，默认为 question_id

    `knowledge_id`: [string] knowledge_id 最终填写的字段名，默认为 knowledge_id

    调用示例
    ------
    ``` python
    .retrieve_answer_type_from_faq_service(
      kess_service="grpc_faq_service",
      from_name="changeItem",
      timeout_ms=200,
      query="退货",
      intent="intent",
      question="question",
      answer="answer",
      answer_type="answer_type"
    )
    ```
    """
    self._add_processor(ShopCustomerFaqAnswerTypeRetriever(kwargs))
    return self

  def retrieve_by_elastic_search(self, **kwargs):
    """
    MerchantElasticSearchRetriever
    ------
    从 elastic search 里面进行召回操作，需要自己写 json 的召回语句。 item_key 来自于随机值或者从某个 es 的属性中确认，score 来自 es 召回的 score。

    参数配置
    ------
    `cluster`: [string] 必填，es 的 ip:port

    `reason`: [int] 召回原因，默认为 0

    `index`: [string] 表示 es 的索引，详细 es 的说明可以见官方文档，这里不做赘述。

    `type`: [string] 表示 es 的类型，同上
    
    `query`: [string] 表示 es 的查询语句，同上

    `timeout_ms`: [int] 超时时间，单位 ms，默认值 200

    `use_random_itemkey`: [int] 是否使用随机 itemkey，为 1 表示 是，为 0 表示否。默认为 1

    `itemkey_from` : [string] 在不使用随机 itemkey 的情况下，指定 itemkey 来自 source 中的哪个字段，该字段必须为 int 类型

    `item_attrs`: [array] string_list 类型，表示获取的 es item_attr 名字，不填即为空。

    `item_attrs_type`: [array] string_list，表示获取的 item_attrs 类型，和上面一一对应，如果为空，则表示都为 string 类型.

    `normalization_socre_attr_name` : [string] 表示是否需要正则化分数，会填写到该字段上
    
    调用示例
    ------
    ``` python
    .retrieve_by_elastic_search(
      cluster="localhost:8000",
      reason=200,
      index="index",
      query="query",
      type="type",
      item_attrs=["thread", "pod_name", "filename"]
    )
    ```
    """
    self._add_processor(MerchantElasticSearchRetriever(kwargs))
    return self

  def enricher_by_es_elastic_search(self, **kwargs):
    """
    MerchantElasticSearchEnricher
    ------
    从 elastic search 里面进行 enricher 操作, 非通用接口，如果不了解请勿使用。

    用以 电商智能客服业务 获取兜底回答的四大 id 的 processor

    参数配置
    ------
    `cluster`: [string] 必填，es 的 ip:port

    `index`: [string] 表示 es 的索引，详细 es 的说明可以见官方文档，这里不做赘述。

    `type`: [string] 表示 es 的类型，同上
    
    `timeout_ms`: [int] 超时时间，单位 ms，默认值 200

    `intent_id`: [string] intent_id 最终填写的字段名，默认为 intent_id

    `answer_id`: [string] answer_id 最终填写的字段名，默认为 answer_id

    `question_id`: [string] question_id 最终填写的字段名，默认为 question_id

    `knowledge_id`: [string] knowledge_id 最终填写的字段名，默认为 knowledge_id
    
    调用示例
    ------
    ``` python
    .enricher_by_es_elastic_search(
      cluster="localhost:8000",
      index="index",
      query="query",
      type="type"
    )
    ```
    """
    self._add_processor(MerchantElasticSearchEnricher(kwargs))
    return self



  def fetch_merchant_user_sample_attr(self, **kwargs):
    """
    MerchantUserSampleAttrEnricher
    ------
    获取电商的用户特征

    参数配置
    ------

    `save_attr_names_to_attr`: [string] 存储sample attr names
    
    `timeout_ms`: [int] 超时时间，单位 ms，默认值 200

    `include_attrs`:[string] 需要用到的attrs
    
    调用示例
    ------
    ``` python
    .fetch_merchant_user_sample_attr(
      save_attr_names_to_attr="merchant_sample_attr_names",
      timeout_ms=200,
      include_attrs=["attr_a","attr_b"]
    )
    ```
    """
    self._add_processor(MerchantUserSampleAttrEnricher(kwargs))
    return self

  def fetch_merchant_follow_cross_attr(self, **kwargs):
    """
    MerchantFollowCrossFeatureEnricher
    ------
    获取电商的关注页交叉特征
    参数配置
    ------
    `input_attr`:[string] follow user info pb name
    `user_id_attr`:[string] user id name
    `author_id_attr`:[string] author id name
    
    调用示例
    ------
    ``` python
    .fetch_merchant_follow_cross_attr(
      input_attr="follow_user_info",
      user_id_attr="uId",
      author_id_attr="aId"
    )
    ```
    """
    self._add_processor(MerchantFollowCrossFeatureEnricher(kwargs))
    return self

  def dcaf_before_tide(self, **kwargs):
    """
    MerchantRecoDcafBeforeTideEnricher
    ------
    一般该 processor 用作自动降级，自动调整参数。能够根据设定的目标，自动化的调整变量，使得影响的值尽量趋近于目标值。

    潮汐自动化调参 Processor，该 Processor 需要配合 CommonRecoAfterTideEnricher 使用。

    有任何疑问可以联系 @卿俊，相关文档：https://docs.corp.kuaishou.com/d/home/<USER>

    参数配置
    ------
    `save_name`: [string] [动态参数] [必填] 获取的值的 common attr 名字，其存储的格式为 IntAttr。

    `default_value`: [int] [动态参数] [必填] 初始的默认值

    `group_name`: [string] [动态参数] 每对 tide processor 的对应名字，需要一一对应。如果忽略，则默认表示代码中仅有一组 Tide Processor。

    调用示例
    ------
    ``` python
    .dcaf_before_tide(
      default_value=200,
      group_name="sort",
      save_name="limit_num"
    )
    ```
    """
    self._add_processor(MerchantRecoDcafBeforeTideEnricher(kwargs))
    return self
  
  def dcaf_after_tide(self, **kwargs):
    """
    MerchantRecoDcafAfterTideEnricher
    ------
    一般该 processor 用作自动降级，自动调整参数。能够根据设定的目标，自动化的调整变量，使得影响的值尽量趋近于目标值。

    潮汐自动化调参 Processor，该 Processor 需要配合 CommonRecoDcafBeforeTideEnricher 使用。

    有任何疑问可以联系 @卿俊，相关文档：https://docs.corp.kuaishou.com/d/home/<USER>

    参数配置
    ------
    `save_name`: [string] [必填] 获取的值的 common attr 名字，格式为 Int Attr。

    `adjust_function`: [string] [必填] 潮汐策略调节方式，有 easy 和 pid 两种方式

    如果在 adjust_function = "pid" 的情况下，你需要填写以下三个参数, kp, kd, ki.
      
    其 pid 公式为 output = -kp * (measures[n - 1] - set_point) - ki * sum(measures[i] - set_point) - kd * (measures[n - 1] - measures[n - 2])：
      - `pid_kp`: [double] [动态参数] 可缺省。 pid 调节的比例项，默认值 0.1。
      - `pid_ki`: [double] [动态参数] 可缺省。 pid 调节的积分项，默认值 0.01。
      - `pid_kd`: [double] [动态参数] 可缺省。 pid 调节的微分项，默认值 0.02。
  
    如果在 adjust_function = "easy" 的情况下，你需要填写以下两个参数，step, window_size:
      - `step`: [int] [动态参数] 可缺省，表示单次变换的步长，默认值为 20
      - `window_size`: [int] [动态参数] 可缺省，表示多少次 QPS 之后进行一次统计，默认值为 20
    
    `max_value`: [int] [动态参数] 存储的 common attr 值的上界，默认 10000

    `min_value`: [int] [动态参数] 存储的 common attr 值的下界，默认 10
    
    `default_value`: [int] [动态参数] 初始的默认值， 默认 1000

    `target`: [int] [动态参数] 追踪的目标值，默认 200

    `from_common_attr`: [string] [动态参数] 表示用来追踪调节目标的 CommonAttr 名字, 仅支持 int 或者 float 类型

    `group_name`: [string] 可缺省，每对 tide processor 的对应名字，需要一一对应。如果忽略，则默认表示代码中仅有一组 Tide Processor。
    
    调用示例
    ------
    ``` python
    .dcaf_after_tide(
      window_size=10,
      target=300,
      step=10,
      default_value=1000,
      adjust_function="easy",
      group_name="sort",
      save_name="limit_num",
      from_common_attr="test"
    )
    ```
    """
    self._add_processor(MerchantRecoDcafAfterTideEnricher(kwargs))
    return self

  def dcaf_user_value(self, **kwargs):
    """
    MerchantRecoDcafUserValueEnricher
    ------
    传入 value 之后，会自动根据历史数据返回排名在多少分位，并输出推荐的分数（仅参考)

    参数配置
    ------
    `user_value`: [double] [动态参数] [必填] 用户价值。

    `save_name`: [double] [动态参数] [必填] 用户排名存储字段名，表示的是排名的分位，例如 0.9 表示 top 90。 -1 表示 未获得排名

    `recommend_name`: [double] [动态参数] [必填] 用户推荐值存储字段名，表示的是最后推荐的位置排名

    调用示例
    ------
    ``` python
    .before_tide(
      user_value="{{user_value}}",
      save_name="rank",
      recommend_name="recommend"
    )
    ```
    """
    self._add_processor(MerchantRecoDcafUserValueEnricher(kwargs))
    return self

  def merchant_common_colossus_parse(self, **kwargs):
    """
    MerchantCommonColossusParseEnricher
    ------
    通用的colossus解析processor，解析colossus中的字段到common attr中
    不支持 repeated 类型的 field
    参数配置
    ------
    `class_name`: [string] 转换的 protobuf 的类型，当前支持所有链接的 Message 类型。
    `colossus_resp_attr`: [string] 输入colossus抽取的信息。
    `attrs`: [list] 需要抽取的属性列表
      - `name`: [string] attr 的名称。
      - `path`: [string] protobuf 结构内数据路径。
    `on_condition`: [string] (可选) 根据哪个条件筛选,默认可以不写
    `filter`: [string] (可选)按照什么条件过滤，支持（>=,<=,>,<,==,!=）
    `compare_to_name`: [string|int] (可选) 满足什么条件的被筛选出来，这个需要的是一个common attr的名字
    调用示例
    ------
    ``` python
    .merchant_common_colossus_parse(
      colossus_resp_attr="colossus_resp",
      class_name="colossus.GoodClickItem",
      attrs=[
        {"name":"pay_time_list", "path":"pay_time"},
        {"name":"buy_item_price_list", "path":"item_price"},
        {"name":"seller_id_list", "path":"real_seller_id"}],
      on_condition = "",
      filter = "<=",
      compare_to_name = ""
    )
    ```
    """
    self._add_processor(MerchantCommonColossusParseEnricher(kwargs))
    return self

  def merchant_common_colossus_batch_parse(self, **kwargs):
    """
    MerchantCommonColossusBatchParseEnricher
    ------
    通用的colossus解析processor，解析colossus中的字段到common attr中
    不支持 repeated 类型的 field，支持batch解析。
    参数配置
    ------
    `class_name`: [string] 转换的 protobuf 的类型，当前支持所有链接的 Message 类型。
    `colossus_resp_attr`: [string] 输入colossus抽取的信息。
    `attrs`: [list] 需要抽取的属性列表
      - `name`: [string] attr 的名称。
      - `path`: [string] protobuf 结构内数据路径。
    `on_condition`: [string] (可选) 根据哪个条件筛选,默认可以不写
    `filter`: [string] (可选)按照什么条件过滤，支持（>=,<=,>,<,==,!=）
    `compare_to_name`: [string|int] (可选) 满足什么条件的被筛选出来，这个需要的是一个common attr的名字
    调用示例
    ------
    ``` python
    .merchant_common_colossus_batch_parse(
      colossus_resp_attr="colossus_resp",
      class_name="colossus.GoodClickItem",
      attrs=[
        {"name":"pay_time_list", "path":"pay_time"},
        {"name":"buy_item_price_list", "path":"item_price"},
        {"name":"seller_id_list", "path":"real_seller_id"}],
      on_condition = "",
      filter = "<=",
      compare_to_name = ""
    )
    ```
    """
    self._add_processor(MerchantCommonColossusBatchParseEnricher(kwargs))
    return self

  def extract_merchant_colossus_click_slice_feature(self, **kwargs):
    """
    MerchantColossusClickSliceFeatureEnricher
    ------
    电商基于点击colossus统计的 slice 特征抽取

    参数配置
    ------

    `colossus_resp_attr`: [string] 从给定 common attr 获取 colossus的结果。
    
    `cart_truncat_num`: [int] 从给定 common attr 获取 小黄车列表截断的数目。

    `target_aid`: [string] 从给定 item attr 获取 aid属性。

    `target_cate1`: [string] 从给定 item attr 获取 小黄车列表一级类目。

    `target_cate2`: [string] 从给定 item attr 获取 小黄车列表二级类目。

    `target_cate3`: [string] 从给定 item attr 获取 小黄车列表三级类目。

    `target_leaf`: [string] 从给定 item attr 获取 小黄车列表叶子类目。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .extract_merchant_colossus_click_slice_feature(
        colossus_resp_attr='colossus_resp_attr',
        target_aid='target_aid',
        target_cate1 ='target_cate1',
        target_cate2 ='target_cate2',
        target_cate3 ='target_cate3',
        target_leaf ='target_leaf',
        print_debug_log = True,
        filter_time = 5,
        cart_truncat_num = 10
        )
    ```
    """
    self._add_processor(MerchantColossusClickSliceFeatureEnricher(kwargs))
    return self
  
  def extract_merchant_colossus_order_slice_feature(self, **kwargs):
    """
    MerchantColossusOrderSliceFeatureEnricher
    ------
    电商基于购买colossus统计的 slice 特征抽取

    参数配置
    ------

    `colossus_resp_attr`: [string] 从给定 common attr 获取 colossus的结果。
    
    `cart_truncat_num`: [int] 从给定 common attr 获取 小黄车列表截断的数目。

    `target_aid`: [string] 从给定 item attr 获取 aid属性。

    `target_cate1`: [string] 从给定 item attr 获取 小黄车列表一级类目。

    `target_cate2`: [string] 从给定 item attr 获取 小黄车列表二级类目。

    `target_cate3`: [string] 从给定 item attr 获取 小黄车列表三级类目。

    `target_leaf`: [string] 从给定 item attr 获取 小黄车列表叶子类目。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .extract_merchant_colossus_order_slice_feature(
        colossus_resp_attr='colossus_resp_attr',
        target_aid='target_aid',
        target_cate1 ='target_cate1',
        target_cate2 ='target_cate2',
        target_cate3 ='target_cate3',
        target_leaf ='target_leaf',
        print_debug_log = True,
        cart_truncat_num = 10
        )
    ```
    """
    self._add_processor(MerchantColossusOrderSliceFeatureEnricher(kwargs))
    return self

  def extract_merchant_repeat_impression(self, **kwargs):
    """
    MerchantRepeatImpressionEnricher
    ------
    电商基于colossus v4 解决重复曝光特征抽取

    参数配置
    ------

    `colossus_resp_attr`: [string] 从给定 common attr 获取 colossus的结果。
    
    `max_user_num_limt`: [int] 最多获取多少个colossus的结果。

    `filter_minute_ago`: [int] 防止穿越，过去多久之前的数据。

    `pid_attr`: [string] 从给定 item attr 获取 target 对应的 live id。

    调用示例
    ------
    ``` python
    .extract_merchant_repeat_impression(
        colossus_resp_attr='colossus_resp_attr',
        max_user_num_limt=2000,
        filter_minute_ago =1,
        pid_attr ='pId'
        )
    ```
    """
    self._add_processor(MerchantRepeatImpressionEnricher(kwargs))
    return self
  
  def extract_merchant_colossus_live_slice_feature(self, **kwargs):
    """
    MerchantColossusLiveSliceFeatureEnricher
    ------
    电商基于colossus v4统计的 slice 特征抽取

    参数配置
    ------

    `colossus_resp_attr`: [string] 从给定 common attr 获取 colossus的结果。

    `target_aid`: [string] 从给定 item attr 获取 aid属性。

    `target_cluster_id`: [string] 从给定 item attr 获取 clusterid属性。

    `filter_time`: [int] 过滤几分钟之前的数据

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .extract_merchant_colossus_click_slice_feature(
        colossus_resp_attr='colossus_resp_attr',
        target_aid='target_aid',
        target_cluster_id ='sEshopLive2mSliceUniSpaceClusterId',
        print_debug_log = True,
        filter_time = 5
        )
    ```
    """
    self._add_processor(MerchantColossusLiveSliceFeatureEnricher(kwargs))
    return self
  
  def extract_merchant_common_mcnt_feature(self, **kwargs):
    """
    MerchantCommonMcntFeatureEnricher
    ------
    电商基于colossus v2统计的 mcnt 特征抽取

    参数配置
    ------

    `colossus_resp_attr`: [string] 从给定 common attr 获取 colossus的结果。

    `common_action_aid_list`: [string] 从给定 common attr 获取 action_aid 的结果。

    `common_action_cate1_list`: [string] 从给定 common attr 获取 action_cate1 的结果。

    `common_action_cate2_list`: [string] 从给定 common attr 获取 action_cate2 的结果。

    `common_action_cate3_list`: [string] 从给定 common attr 获取 action_cate3 的结果。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    `filter_time`: [int] 过滤几分钟之前的数据
    
    `target_aid`: [string] 从给定 item attr 获取 aid属性。

    `target_cate1`: [string] 从给定 item attr 获取 target_cate1 属性    

    `target_cate2`: [string] 从给定 item attr 获取 target_cate2 属性

    `target_cate3`: [string] 从给定 item attr 获取 target_cate3 属性

    调用示例
    ------
    ``` python
    .extract_merchant_colossus_click_slice_feature(
        colossus_resp_attr='colossus_resp_attr',
        common_action_category_list='rs_category_id',
        target_aid='target_aid',
        target_cate1 ='target_cate1',
        target_cate2 ='target_cate2',
        target_cate3 ='target_cate3',
        print_debug_log = True,
        filter_time = 0
        )
    ```
    """
    self._add_processor(MerchantCommonMcntFeatureEnricher(kwargs))
    return self
  
  def extract_merchant_common_mcnt_feature_v2(self, **kwargs):
    """
    MerchantCommonMcntFeatureEnricherV2
    ------
    电商基于colossus v2统计的 mcnt 特征抽取

    参数配置
    ------

    `colossus_resp_attr`: [string] 从给定 common attr 获取 colossus的结果。

    `common_action_aid_list`: [string] 从给定 common attr 获取 action_aid 的结果。

    `common_action_cate1_list`: [string] 从给定 common attr 获取 action_cate1 的结果。

    `common_action_cate2_list`: [string] 从给定 common attr 获取 action_cate2 的结果。

    `common_action_cate3_list`: [string] 从给定 common attr 获取 action_cate3 的结果。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    `filter_time`: [int] 过滤几分钟之前的数据
    
    `target_aid`: [string] 从给定 item attr 获取 aid属性。

    `target_cate1`: [string] 从给定 item attr 获取 target_cate1 属性    

    `target_cate2`: [string] 从给定 item attr 获取 target_cate2 属性

    `target_cate3`: [string] 从给定 item attr 获取 target_cate3 属性

    调用示例
    ------
    ``` python
    .extract_merchant_colossus_click_slice_feature(
        colossus_resp_attr='colossus_resp_attr',
        common_action_category_list='rs_category_id',
        target_aid='target_aid',
        target_cate1 ='target_cate1',
        target_cate2 ='target_cate2',
        target_cate3 ='target_cate3',
        print_debug_log = True,
        filter_time = 0
        )
    ```
    """
    self._add_processor(MerchantCommonMcntFeatureEnricherV2(kwargs))
    return self

  def merchant_colossus_cart_gsu(self, **kwargs):
    """
    MerchantColossusCartGSUEnricher
    ------
    电商基于colossus 人货匹配建模

    参数配置
    ------

    `click_colossus_resp_attr`: [string] 从给定 common attr 获取 点击colossus的结果。

    `order_colossus_resp_attr`: [string] 从给定 common attr 获取 购买colossus的结果。
    
    `max_user_num_limt`: [int] 获取用户购买的个数。

    `cart_gsu_feature_prefix`: [string] 获取抽取的item 侧特征的前缀。

    `filter_request_time`: [string] 从给定 common attr 获取过滤的时间戳。

    `side_info_attr_lists`: [list] 从给定 item attr 获取要抽取的side info。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .merchant_colossus_cart_gsu(
        click_colossus_resp_attr='click_colossus_resp',
        order_colossus_resp_attr='order_colossus_resp',
        max_user_num_limt=50,
        cart_gsu_feature_prefix ='gsu_select_order_leaf_match',
        filter_request_time ='request_time',
        side_info_attr_lists =['sCartItemList', 'sCartItemCategoryLeafIdList',
                          "sCartItemIsInterpretingList", "sCartItemBrandIdList", 'sCartItemMerchantEntityIdList'],
        )
    ```
    """
    self._add_processor(MerchantColossusCartGSUEnricher(kwargs))
    return self

  def merchant_colossus_cart_gsu_new(self, **kwargs):
    """
    MerchantColossusCartGSUNewEnricher
    ------
    电商基于colossus 人货匹配建模

    参数配置
    ------

    `click_colossus_resp_attr`: [string] 从给定 common attr 获取 点击colossus的结果。

    `order_colossus_resp_attr`: [string] 从给定 common attr 获取 购买colossus的结果。
    
    `max_user_num_limt`: [int] 获取用户购买的个数。

    `cart_gsu_feature_prefix`: [string] 获取抽取的item 侧特征的前缀。

    `filter_request_time`: [string] 从给定 common attr 获取过滤的时间戳。

    `side_info_attr_lists`: [list] 从给定 item attr 获取要抽取的side info。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .merchant_colossus_cart_gsu_new(
        click_colossus_resp_attr='click_colossus_resp',
        order_colossus_resp_attr='order_colossus_resp',
        max_user_num_limt=50,
        cart_gsu_feature_prefix ='gsu_select_order_leaf_match',
        filter_request_time ='request_time',
        side_info_attr_lists =['sCartItemList', 'sCartItemCategoryLeafIdList',
                          "sCartItemIsInterpretingList", "sCartItemBrandIdList", 'sCartItemMerchantEntityIdList'],
        )
    ```
    """
    self._add_processor(MerchantColossusCartGSUNewEnricher(kwargs))
    return self
  
  def merchant_colossus_cart_gsu_new_opt(self, **kwargs):
    """
    MerchantColossusCartGSUNewOptEnricher
    ------
    电商基于colossus高性能解析的 人货匹配建模

    参数配置
    ------

    `click_colossus_resp_attr`: [string] 从给定 common attr 获取 点击colossus的结果。

    `order_colossus_resp_attr`: [string] 从给定 common attr 获取 购买colossus的结果。
    
    `max_user_num_limt`: [int] 获取用户购买的个数。

    `cart_gsu_feature_prefix`: [string] 获取抽取的item 侧特征的前缀。

    `filter_request_time`: [string] 从给定 common attr 获取过滤的时间戳。

    `side_info_attr_lists`: [list] 从给定 item attr 获取要抽取的side info。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .merchant_colossus_cart_gsu_new_opt(
        click_colossus_resp_attr='click_colossus_resp',
        order_colossus_resp_attr='order_colossus_resp',
        max_user_num_limt=50,
        cart_gsu_feature_prefix ='gsu_select_order_leaf_match',
        filter_request_time ='request_time',
        side_info_attr_lists =['sCartItemList', 'sCartItemCategoryLeafIdList',
                          "sCartItemIsInterpretingList", "sCartItemBrandIdList", 'sCartItemMerchantEntityIdList'],
        )
    ```
    """
    self._add_processor(MerchantColossusCartGSUNewOptEnricher(kwargs))
    return self

  def merchant_colossus_cart_gsu_v2(self, **kwargs):
    """
    MerchantColossusCartGSUEnricherV2Enricher
    ------
    电商基于colossus 人货匹配建模

    参数配置
    ------

    `click_colossus_resp_attr`: [string] 从给定 common attr 获取 点击colossus的结果。

    `order_colossus_resp_attr`: [string] 从给定 common attr 获取 购买colossus的结果。
    
    `max_user_num_limt`: [int] 获取用户购买的个数。

    `cart_gsu_feature_prefix`: [string] 获取抽取的item 侧特征的前缀。

    `filter_request_time`: [string] 从给定 common attr 获取过滤的时间戳。

    `side_info_attr_lists`: [list] 从给定 item attr 获取要抽取的side info。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .merchant_colossus_cart_gsu(
        click_colossus_resp_attr='click_colossus_resp',
        order_colossus_resp_attr='order_colossus_resp',
        max_user_num_limt=50,
        cart_gsu_feature_prefix ='gsu_select_order_leaf_match',
        filter_request_time ='request_time',
        side_info_attr_lists =['sCartItemList', 'sCartItemCategoryLeafIdList',
                          "sCartItemIsInterpretingList", "sCartItemBrandIdList", 'sCartItemMerchantEntityIdList'],
        )
    ```
    """
    self._add_processor(MerchantColossusCartGSUEnricherV2Enricher(kwargs))
    return self

  def truncate_by_bucket_flag(self, **kwargs):
    """
    MerchantRecoBucketTruncateArranger
    ------
    粗排分桶保量截断

    参数配置
    ------
    `size_limit`: [int] [动态参数] 当前结果集最多保留多少个 item ，需大于或等于 0
    `reserved_quota`: [int] [动态参数] 分桶保量的总 quota
    `append_reason_to`: [string] 重复的 reason 追加保存到指定的 int_list 类型 item_attr 中
    `buckets`: [list] 每个桶的保量信息
      - `flag`: [string] 该桶的 item 属性标志
      - `ratio`: [double] [动态参数] 该桶保量的 quota 占总 quota 的比例
      - `priority`: [int] 该桶保量的优先级

    调用示例
    ------
    ``` python
    .truncate_by_bucket_flag(
      size_limit = 50,
      reserved_quota = 6,
      append_reason_to = "dup",
      buckets = [
        {
            "flag": "force_insert_bucket",
            "ratio":0.5,
            "priority":15
        },
        {
            "flag": "follow_bucket",
            "ratio":0.2,
            "priority":11
        }
      ]
    )
    ```
    """
    self._add_processor(MerchantRecoBucketTruncateArranger(kwargs))
    return self
  
  def cold_start_truncate_by_bucket(self, **kwargs):
    """
    MerchantRecoColdStartBucketTruncateArranger
    ------
    冷起粗排分桶保量截断

    参数配置
    ------
    `size_limit`: [int] [动态参数] 必配项,当前结果集最多保留多少个 item, 需大于或等于 0
    `bucket_params`: [string] [动态参数] 必配项,分桶保量比例参数,保量后不够size_limit则从不属于任何分桶的资源内补齐(按mc_score_cs排序取top)
    `sort_params`: [string] [动态参数] 选配项,分桶内排序的item_attr字段,默认按mc_score_cs排序
    `enable_bucket_mutual_exclusion`: [int] [动态参数] 选配项,分桶内要求资源是否互斥,默认值为1表示互斥

    调用示例
    ------
    ``` python
    .cold_start_truncate_by_bucket(
      size_limit = 200,
      bucket_params = "b1:0.3,b2:0.1,b3:0.1,b4:0.2",
      sort_params = "b2:mc_es_score"
      enable_bucket_mutual_exclusion = 1
    )
    ```
    """
    self._add_processor(MerchantRecoColdStartBucketTruncateArranger(kwargs))
    return self

  def merchant_gsu_aid_list(self, **kwargs):
    """
    MerchantGsuAidListEnricher
    ------
    根据colossus获取用户观看过的author_id_attr, 保存至common_attr colossus_aid_list中
    参数
    ------
    `time_interval`: [double] [动态参数] 要提取从什么时间开始到现在的attr，单位天,-1.0表示不限制时间
    `default_cur_day`: [int] 是否从当日凌晨开始计算, 大于-1表示是
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `only_show_target`: [int] [动态参数] 是否只返回曝光的直播作者id, 1表示是，0表示曝光并且观看
    `valid_watch_time_threshold`: [double] [动态参数] 有效观看时长阈值，单位秒
    `result_deduplicate`: [int] [动态参数] 返回的作者id是否要去重
    `save_to`: [string] 返回的作者 id 保存的common_attr名字
    调用示例
    ------
    ``` python
    .merchant_gsu_aid_list(
        result_deduplicate=1,
        only_show_target=0,
        colossus_resp_attr='colossus_resp',
        valid_watch_time_threshold=5.0,
        time_interval=-1.0,
        save_to = "watch_aid_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAidListEnricher(kwargs))
    return self    

  def merchant_gsu_auto_aid_list(self, **kwargs):
    """
    MerchantGsuAutoAidListEnricher
    ------
    根据colossus获取用户观看过的author_id_attr, 保存至common_attr colossus_aid_list中
    参数
    ------
    `time_interval`: [double] [动态参数] 要提取从什么时间开始到现在的attr，单位天,-1.0表示不限制时间
    `default_cur_day`: [int] 是否从当日凌晨开始计算, 大于-1表示是
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `only_show_target`: [int] [动态参数] 是否只返回曝光的直播作者id, 1表示是，0表示曝光并且观看
    `valid_watch_time_threshold`: [double] [动态参数] 有效观看时长阈值(间内+间外观看时长)，单位秒
    `result_deduplicate`: [int] [动态参数] 返回的作者id是否要去重
    `save_to`: [string] 返回的作者 id 保存的common_attr名字
    调用示例
    ------
    ``` python
    .merchant_gsu_auto_aid_list(
        result_deduplicate=1,
        only_show_target=0,
        colossus_resp_attr='colossus_resp',
        valid_watch_time_threshold=5.0,
        time_interval=-1.0,
        save_to = "watch_aid_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAutoAidListEnricher(kwargs))
    return self

  def merchant_gsu_auto_aid_list_v2(self, **kwargs):
    """
    MerchantGsuAutoAidListEnricher
    ------
    根据colossus获取用户观看过的author_id_attr, 保存至common_attr colossus_aid_list中
    参数
    ------
    `time_interval`: [double] [动态参数] 要提取从什么时间开始到现在的attr，单位天,-1.0表示不限制时间

    `default_cur_day`: [int] 是否从当日凌晨开始计算, 大于-1表示是
    
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    
    `only_show_target`: [int] [动态参数] 是否只返回曝光的直播作者id, 1表示是，0表示曝光并且观看
    
    `valid_watch_time_threshold`: [double] [动态参数] 有效观看时长阈值(间内+间外观看时长)，单位秒
    
    `result_deduplicate`: [int] [动态参数] 返回的作者id是否要去重
    
    `save_to`: [string] 返回的作者 id 保存的common_attr名字
    调用示例
    ------
    ``` python
    .merchant_gsu_auto_aid_list_v2(
        result_deduplicate=1,
        only_show_target=0,
        colossus_resp_attr='colossus_resp',
        valid_watch_time_threshold=5.0,
        time_interval=-1.0,
        save_to = "watch_aid_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAutoAidListV2Enricher(kwargs))
    return self    
  
  def merchant_photo_gsu_from_aid_cluster_id(self, **kwargs):
    """
    MerchantPhotoGsuFromAidClusterIdEnricher
    ------
    从liveItemV4 colossus进行aid cluster id匹配检索
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `kess_service`: [string] [动态参数] aid cluster id请求服务

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_from_aid_cluster_id(
      colossus_resp_attr='live_colossus_resp',
      limit_num_attr=100,
      timeout_ms=10000,
      kess_service='mmu_all_ad_aid_cluster_emb_query_server',
      shards=1,
      target_cluster_attr='photo_aid_cluster_id',
      max_pids_per_request=2000,
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuFromAidClusterIdEnricher(kwargs))
    return self   

  def merchant_live_gsu_from_aid_cate(self, **kwargs):
    """
    MerchantLiveGsuFromAidCateEnricher
    ------
    从liveItemV4 colossus进行类目硬匹配检索
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `kess_service`: [string] [动态参数] 请求的aid到类目的映射服务

    调用示例
    ------
    ``` python
    .merchant_live_gsu_from_aid_cate(
        colossus_resp_attr='live_colossus_resp',
        limit_num_attr=50,
        timeout_ms=10000,
        kess_service='grpc_eshopHotAid2CateByGmv',
        shards=1,
        filter_old_days_flag_attr = False,
        target_cate1_attr='photo_goods_iCate1Id',
        target_cate2_attr='photo_goods_iCate2Id',
        target_cate3_attr='photo_goods_iCate3Id',
        max_pids_per_request=1000,
    )
    ```
    """
    self._add_processor(MerchantLiveGsuFromAidCateEnricher(kwargs))
    return self     

  def merchant_photo_gsu_from_good_info_v2(self, **kwargs):
    """
    MerchantPhotoGsuFromGoodInfoV2Enricher
    ------
    从视频 colossus v2 进行挂车视频检索 v2
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_from_good_info_v2(
      colossus_resp_attr='colossus_resp',
      limit_num_attr='limit_num',
      timeout_ms=100,
      kess_service='kws-mmu-beijing-embedding-merchant-photo-iteminfo-emb',
      shards=1,
      target_cluster_attr='item_emb',
      max_pids_per_request=500,
      thread_num=4,
      cate1_match_attr=True,
      cate2_match_attr=True,
      cate3_match_attr=True,
      entity_match_attr=True,
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuFromGoodInfoV2Enricher(kwargs))
    return self
  
  def merchant_photo_gsu_from_good_info_v2_column_enricher(self, **kwargs):
    """
    MerchantPhotoGsuFromGoodInfoV2Enricher
    ------
    从视频 colossus v2 input colossus list attr 进行挂车视频检索 v2
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_from_good_info_v2_column_enricher(
      limit_num_attr='limit_num',
      timeout_ms=100,
      kess_service='kws-mmu-beijing-embedding-merchant-photo-iteminfo-emb',
      shards=1,
      target_cluster_attr='item_emb',
      max_pids_per_request=500,
      thread_num=4,
      cate1_match_attr=True,
      cate2_match_attr=True,
      cate3_match_attr=True,
      entity_match_attr=True,
      input_photo_id_attr = "video_item_photo_id",
      input_author_id_attr = "video_item_author_id",
      input_duration_attr = "video_item_duration",
      input_play_time_attr = "video_item_play_time",
      input_tag_attr = "video_item_tag",
      input_channel_attr = "video_item_channel",
      input_label_attr = "video_item_label",
      input_timestamp_attr = "video_item_timestamp",
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuFromGoodInfoV2ColumnEnricher(kwargs))
    return self
  
  def merchant_photo_gsu_from_good_info_v3(self, **kwargs):
    """
    MerchantPhotoGsuFromGoodInfoV3Enricher
    ------
    从视频 colossus v2 进行挂车视频检索 v3
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_from_good_info_v3(
      colossus_resp_attr='colossus_resp',
      limit_num_attr='limit_num',
      timeout_ms=100,
      kess_service='kws-mmu-beijing-embedding-merchant-photo-iteminfo-emb',
      shards=1,
      target_cate1_attr='cate1_list',
      target_cate2_attr='cate2_list',
      target_cate3_attr='cate3_list',
      max_pids_per_request=500,
      thread_num=4,
      cate1_match_attr=False,
      cate2_match_attr=False,
      # cate3_match_attr=False,
      # entity_match_attr=False,
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuFromGoodInfoV3Enricher(kwargs))
    return self
  
  def merchant_photo_gsu_good_cate(self, **kwargs):
    """
    MerchantPhotoGsuGoodCate
    ------
    对电商视频序列做类目gsu检索
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_good_cate(
      colossus_resp_attr='merchant_video_item_colossus_resp',
      limit_num_attr=50,
      photo_id_list_attr="cart_photo_gsu_pid_list_new",
      author_id_list_attr="cart_photo_gsu_aid_list_new",
      duration_list_attr="cart_photo_gsu_duration_list_new",
      play_time_list_attr="cart_photo_gsu_play_time_list_new",
      play_lag_list_attr="cart_photo_gsu_lag_list_new",
      channel_list_attr="cart_photo_gsu_channel_list_new",
      label_list_attr="cart_photo_gsu_label_list_new",
      index_list_attr="cart_photo_gsu_index_list_new",
      cate1_list_attr="cart_photo_gsu_cate1_list_new",
      cate2_list_attr="cart_photo_gsu_cate2_list_new",
      cate3_list_attr="cart_photo_gsu_cate3_list_new",
      target_cate1_attr='photo_goods_iCate1Id',
      target_cate2_attr='photo_goods_iCate2Id',
      target_cate3_attr='photo_goods_iCate3Id'
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuGoodCateEnricher(kwargs))
    return self
  
  def merchant_photo_gsu_good_semantic(self, **kwargs):
    """
    MerchantPhotoGsuGoodSemantic
    ------
    对电商视频序列做类目gsu检索
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_photo_gsu_good_semantic(
      limit_num_attr=50,
      n_minute_ago=0,
      photo_id_list_attr="new_photo_colossus_photo_id_list",
      author_id_list_attr="new_photo_colossus_aid_list",
      play_time_list_attr="new_photo_colossus_play_time_list",
      duration_list_attr="new_photo_colossus_duration_list",
      channel_list_attr="new_photo_colossus_channel_list",
      label_list_attr="new_photo_colossus_label_list",
      timestamp_list_attr="new_photo_colossus_timestamp_list",
      spu_list_attr="new_photo_colossus_spu_id_list",
      category_list_attr="new_photo_colossus_category_list",
      item_id_list_attr="new_photo_colossus_item_id_list",
      semantic_id_list_attr="new_photo_colossus_item_code_list",
      match_photo_id_list_attr="match_photo_colossus_photo_id_list",
      match_author_id_list_attr="match_photo_colossus_aid_list",
      match_play_time_list_attr="match_photo_colossus_play_time_list",
      match_duration_list_attr="match_photo_colossus_duration_list",
      match_channel_list_attr="match_photo_colossus_channel_list",
      match_label_list_attr="match_photo_colossus_label_list",
      match_spu_list_attr="match_photo_colossus_spu_id_list",
      match_cate1_list_attr="match_photo_colossus_cate1_list",
      match_cate2_list_attr="match_photo_colossus_cate2_list",
      match_cate3_list_attr="match_photo_colossus_cate3_list",
      match_item_id_list_attr="match_photo_colossus_photo_item_id_list",
      match_semantic1_list_attr="match_photo_colossus_photo_code1_list",
      match_semantic2_list_attr="match_photo_colossus_photo_code2_list",
      match_semantic3_list_attr="match_photo_colossus_photo_code3_list",
      match_play_lag_list_attr="match_photo_colossus_lag_list",
      match_index_list_attr="match_photo_colossus_index_list",
      target_level_attr='item_code_id',
    )
    ```
    """
    self._add_processor(MerchantPhotoGsuGoodSemanticEnricher(kwargs))
    return self
 
  def merchant_gsu_from_context(self, **kwargs):
    """
    MerchantGsuFromContext
    ------
    对给定序列做gsu检索
    ------
    `limit_num`: [int] [动态参数] 序列长度, 默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true

    调用示例
    ------
    ``` python
    .merchant_gsu_from_context(
      limit_num_attr='limit_num',
      target_cluster_attr='item_emb',
      cate1_match_attr=True,
      cate2_match_attr=True,
      cate3_match_attr=True,
    )
    ```
    """
    self._add_processor(MerchantGsuFromContextEnricher(kwargs))
    return self

  
  def merchant_photo_short_list(self, **kwargs):
    """
    MerchantPhotoShortListEnricher
    ------
    从视频 colossus v2 中解析短期list
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true
    `only_cart_photo_attr`: [bool] [动态参数] 请求时间戳过滤，默认 false

    调用示例
    ------
    ``` python
    .merchant_photo_short_list(
      limit_num=50,
      colossus_resp_attr="colossus_resp",
      filter_future_attr=True,
      only_cart_photo_attr=True,
      photo_id_list_attr="cart_photo_short_pid_list",
      author_id_list_attr="cart_photo_short_aid_list",
      duration_list_attr="cart_photo_short_duration_list",
      play_time_list_attr="cart_photo_short_play_time_list",
      play_lag_list_attr="cart_photo_short_lag_list",
      tag_list_attr="cart_photo_short_tag_list",
      channel_list_attr="cart_photo_short_channel_list",
      label_list_attr="cart_photo_short_label_list",
      index_list_attr="cart_photo_short_index_list",
    )
    ```
    """
    self._add_processor(MerchantPhotoShortListEnricher(kwargs))
    return self
  
  def merchant_extract_kuiba_user_attr(self, **kwargs):
    """
    KuibaUserAttrEnricher
    -----
    从 context 提取 kuiba::PredictItem , 并将其中的 SampleAttr 存入 context 中

    参数配置
    ------
    `predict_item`: [string] kuiba::PredictItem 获取字段名

    `output_attrs`: [list[string]] 需要从 PredictItem 中抽取的 attr，为空则抽取全部的 attr

    `save_all_item_names`: [string] kuiba::PredictItem 中包含的属性名字

    调用示例
    ------
    ``` python
    .merchant_extract_kuiba_user_attr(
        output_attrs = ["uId", "dId"],
        predict_item = "kuiba_user_attr",
        save_all_item_names = "kuiba_all_item_names")
    ```
    """
    self._add_processor(KuibaUserAttrEnricher(kwargs))
    return self

  def merchant_photo2live_click_list(self, **kwargs):
    """
    MerchantPhoto2LiveClickListEnricher
    ------
    从视频 colossus v2 中解析短期list
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true
    `only_merchant_live_attr`: [bool] [动态参数] 电商直播过滤，默认 false
    `only_photo2live_attr`: [bool] [动态参数] photo2live来源过滤，默认 false
    `n_minute_ago`: [int] [动态参数] 前移时间以分钟为单位，默认0

    调用示例
    ------
    ``` python
    .merchant_photo2live_click_list(
      colossus_resp_attr="live_colossus_resp",
      limit_num=100,
      filter_future_attr=True,
      only_merchant_live_attr=True,
      only_photo2live_attr=True,
      n_minute_ago=1,
    )
    ```
    """
    self._add_processor(MerchantPhoto2LiveClickListEnricher(kwargs))
    return self    

  def merchant_photo2live_click_list_v2(self, **kwargs):
    """
    MerchantPhoto2LiveClickListV2Enricher
    ------
    从视频 colossus v2 中解析短期list
    ------
    `limit_num`: [int] [动态参数] 短期序列长度，默认50
    `filter_future_attr`: [bool] [动态参数] 请求时间戳过滤，默认 true
    `only_merchant_live_attr`: [bool] [动态参数] 电商直播过滤，默认 false
    `only_photo2live_attr`: [bool] [动态参数] photo2live来源过滤，默认 false
    `n_minute_ago`: [int] [动态参数] 前移时间以分钟为单位，默认0

    调用示例
    ------
    ``` python
    .merchant_photo2live_click_list_v2(
      colossus_resp_attr="live_colossus_resp",
      limit_num=100,
      filter_future_attr=True,
      only_merchant_live_attr=True,
      only_photo2live_attr=True,
      n_minute_ago=1,
    )
    ```
    """
    self._add_processor(MerchantPhoto2LiveClickListV2Enricher(kwargs))
    return self 

  def merchant_query_intent_list(self, **kwargs):
    """
    MerchantQueryIntentListEnricher
    ------
    解析电商搜索复杂list
    ------
    `filter_day_attr_`: [double] [动态参数] 要提取从什么时间开始到现在的attr，单位天,-1表示不限制时间

    调用示例
    ------
    ``` python
    .merchant_query_intent_list()
    ```
    """
    self._add_processor(MerchantQueryIntentListEnricher(kwargs))
    return self 
  
  def aid2aid_photo_v2(self, **kwargs):
    """
    MerchantGsuAid2AidPhoto2Enricher
    ------
    aid2aid 检索短视频 colossus v2 
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据

    调用示例
    ------
    ``` python
    .aid2aid_photo_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      filter_future_attr=True,
      target_attr = "aId"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidPhoto2Enricher(kwargs))
    return self 
  
  def aid2aid_photo_common_v2(self, **kwargs):
    """
    MerchantGsuAid2AidPhotoCommon2Enricher 
    ------
    aid2aid 检索短视频 colossus v2 
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据

    调用示例
    ------
    ``` python
    .aid2aid_photo_common_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      limit_per_aid_num = 10,
      filter_future_attr=True,
      target_attr = "aid_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidPhotoCommon2Enricher(kwargs))
    return self 
 
  def aid2aidlist_live_common(self, **kwargs):
    """
    MerchantGsuAid2AidLiveCommonEnricher 
    ------
    aid2aid 检索直播 colossus v3 
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据

    调用示例
    ------
    ``` python
    .aid2aidlist_live_common(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      limit_per_aid_num = 10,
      filter_future_attr=True,
      filter_play_time = 10,
      target_attr = "aid_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidLiveCommonEnricher(kwargs))
    return self

  def aid2aidlist_photo_v2(self, **kwargs):
    """
    MerchantGsuAid2AidListPhoto2Enricher
    ------
    aid2aid 检索短视频 colossus v2 
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据

    调用示例
    ------
    ``` python
    .aid2aidlist_photo_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      limit_per_aid_num = 10,
      filter_play_time = 10,
      filter_future_attr=True,
      target_attr = "aId",
      target_attr_list = "aId_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidListPhoto2Enricher(kwargs))
    return self 
  
  def aid2aidlist_good_click_v2(self, **kwargs):
    """
    MerchantGsuAid2AidListGoodClick2Enricher
    ------
    aid2aid 检索商品点击 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .aid2aidlist_good_click_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      limit_per_aid_num = 10,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId",
      target_attr_list = "aId_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidListGoodClick2Enricher(kwargs))
    return self 

  def aid2aidlist_good_order_v2(self, **kwargs):
    """
    MerchantGsuAid2AidListGoodOrder2Enricher
    ------
    aid2aid 检索商品购买 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .aid2aidlist_good_order_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 20,
      limit_per_aid_num = 10,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId",
      target_attr_list = "aId_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidListGoodOrder2Enricher(kwargs))
    return self

  def aid2aid_live_v2(self, **kwargs):
    """
    MerchantGsuAid2AidLive2Enricher
    ------
    aid2aid 检索直播 colossus v4
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .aid2aid_live_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidLive2Enricher(kwargs))
    return self 
 
  
  def aid2aidlist_live_v2(self, **kwargs):
    """
    MerchantGsuAid2AidListLive2Enricher
    ------
    aid2aidlist 检索直播 colossus v4
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `limit_per_aid_num`: [int] 每个主播截断数量
    `filter_play_time`: [int] 时长过滤
    `target_attr`: [string] 检索 attr name, 一般是 aId_list
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .aid2aidlist_live_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      limit_per_aid_num = 10,
      filter_play_time = 10,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      if_common_target_attr=False,
      target_attr = "aId",
      target_attr_list = "aId_list",
      common_target_attr_list = "common_target_attr_list"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidListLive2Enricher(kwargs))
    return self 
 
  def aid2aid_session_live(self, **kwargs):
    """
    MerchantGsuAid2AidSessionLiveEnricher
    ------
    aid2aidlist 检索直播 colossus v4
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `session_num`: [int] session最近数量
    `session_time`: [int] session最近分钟时长
    `filter_play_time`: [int] 时长过滤
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
    `if_danlie_reco`: [bool]是否过滤单列的直播
    `if_recent_click`: [bool]是否过滤点击
    `if_click_cart`: [bool]是否过滤点击购物车

    调用示例
    ------
    ``` python
    .aid2aid_session_live(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      session_num = 50,
      session_time = 4320,
      filter_play_time = 0,
      filter_future_attr=True,
      if_parse_to_pb_attr=True,
      if_danlie_reco=True,
      if_recent_click=True,
      if_click_cart=False,
      target_attr = "aId"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidSessionLiveEnricher(kwargs))
    return self 

  def merchant_count_aid_live(self, **kwargs):
    """
    MerchantCountAidLiveEnricher
    -----
    aid2aidlist 检索直播 colossus v4
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    调用示例
    ------
    ``` python
    .merchant_count_aid_live(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      target_attr = "aId"
    )
    ```
    """
    self._add_processor(MerchantCountAidLiveEnricher(kwargs))
    return self

  def aid2aid_good_click_v2(self, **kwargs):
    """
    MerchantGsuAid2AidGoodClick2Enricher
    ------
    aid2aid 检索商品点击 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .aid2aid_good_click_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidGoodClick2Enricher(kwargs))
    return self 

  def cate_search_good_click(self, **kwargs):
    """
    MerchantGsuCateSearchGoodsClickEnricher
    ------
    aid2aid 检索商品点击 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .cate_search_good_click(
      colossus_resp_attr = "colossus_resp",
      limit_num = 50,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "iCate2Id"
    )
    ```
    """
    self._add_processor(MerchantGsuCateSearchGoodsClickEnricher(kwargs))
    return self 


  def realshow_enrich_sideinfo_and_gsu(self, **kwargs):
      """
      MerchantGoodsRsSideInfoEnricher
      ------
      丰富商品曝光colossus宽度，并使用类别gsu检索
      ------
      `colossus_resp_attr`: [string] colossus_resp 输出的 attr
      `limit_num`: [int] 截断数量
      `use_other_seq`: [bool] 是否使用商品点击、下单序列和挂车序列的信息。默认True
      调用示例
      ------
      ``` python
      .realshow_enrich_sideinfo_and_gsu(
          limit_num_attr=500,
          gsu_limit_num_attr=100,
          seconds_to_lookback_attr=11,
      )
      ```
      """
      self._add_processor(MerchantGoodsRsSideInfoEnricher(kwargs))
      return self 
  
  def realshow_enrich_sideinfo_live_and_gsu(self, **kwargs):
      """
      MerchantGoodsRsSideInfoLiveEnricher
      ------
      丰富商品曝光colossus宽度，并使用类别gsu检索for直播
      ------
      `colossus_resp_attr`: [string] colossus_resp 输出的 attr
      `limit_num`: [int] 截断数量
      `target_attr`: [string] 检索 attr name, 一般是 aId
      `filter_future_attr`: [bool] 是否过滤可能穿越的数据
      `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
      调用示例
      ------
      ``` python
      .realshow_enrich_sideinfo_live_and_gsu(
          limit_num_attr=500,
          gsu_limit_num_attr=100,
          seconds_to_lookback_attr=11,
      )
      ```
      """
      self._add_processor(MerchantGoodsRsSideInfoLiveEnricher(kwargs))
      return self 

  def realshow_enrich_sideinfo_live_and_gsu(self, **kwargs):
      """
      MerchantGoodsRsSideInfoLiveEnricher
      ------
      丰富商品曝光colossus宽度，并使用类别gsu检索for直播
      ------
      `colossus_resp_attr`: [string] colossus_resp 输出的 attr
      `limit_num`: [int] 截断数量
      `target_attr`: [string] 检索 attr name, 一般是 aId
      `filter_future_attr`: [bool] 是否过滤可能穿越的数据
      `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
      调用示例
      ------
      ``` python
      .realshow_enrich_sideinfo_live_and_gsu(
          limit_num_attr=500,
          gsu_limit_num_attr=100,
          seconds_to_lookback_attr=11,
      )
      ```
      """
      self._add_processor(MerchantGoodsRsSideInfoLiveEnricher(kwargs))
      return self 

  def extract_cart_item_match_attrs(self, **kwargs):
    """
    MerchantExtractCartItemMatchAttrsEnricher
    ------
    提取正在讲解和小黄车顶部商品列表

    参数配置
    ------
    `retr_item_id_list`: [string] 召回的商品id list
    `recent_cate3_id_list`: [string] 用户最近点击的三级类目id list
    `cart_item_id_list`: [string] 小黄车商品id list
    `cart_item_cate3_id_list`: [string] 小黄车商品三级类目id list
    `cart_item_is_interpreting_list`: [string] 小黄车商品是否正在讲解的标记 list

    调用示例
    ------
    ``` python
    .extract_cart_item_match_attrs(
        retr_item_id_list="retr_item_id_list",
        recent_cate3_id_list="recent_cate3_id_list",
        cart_item_id_list="sCartItemList",
        cart_item_cate3_id_list="sCartItemCate3IdList",
        cart_item_is_interpreting_list="sCartItemIsInterpretingList",
    )
    ```
    """
    self._add_processor(MerchantExtractCartItemMatchAttrsEnricher(kwargs))
    return self 


  def extract_item_cate_match_score(self, **kwargs):
    """
    MerchantExtractItemCateMatchScoreEnricher
    ------
    计算给定的商品ID和类目与小黄车商品的匹配分数

    参数配置
    ------
    `input_item_id_list_attr`: [string] 输入的商品id list
    `input_cate1_id_list_attr`: [string] 输入的商品一级类目id list
    `input_cate2_id_list_attr`: [string] 输入的商品二级类目id list
    `input_cate3_id_list_attr`: [string] 输入的商品三级类目id list
    `cart_item_id_list_attr`: [string] 小黄车商品id list
    `cart_item_cate1_id_list_attr`: [string] 小黄车商品一级类目id list
    `cart_item_cate2_id_list_attr`: [string] 小黄车商品二级类目id list
    `cart_item_cate3_id_list_attr`: [string] 小黄车商品三级类目id list
    `cart_item_is_interpreting_list_attr`: [string] 小黄车商品是否正在讲解的标记 list
    `item_match_weight`: [double][动态参数] 商品id匹配分数权重
    `cate1_match_weight`: [double][动态参数] 一级类目匹配分数权重
    `cate2_match_weight`: [double][动态参数] 二级类目匹配分数权重
    `cate3_match_weight`: [double][动态参数] 三级类目匹配分数权重
    `output_live_match_score_attr`: [string] 输出的小黄车商品匹配分数
    `output_item_match_score_attr`: [string] 输出的商品id匹配分数
    `output_cate1_match_score_attr`: [string] 输出的商品一级类目匹配分数
    `output_cate2_match_score_attr`: [string] 输出的商品二级类目匹配分数
    `output_cate3_match_score_attr`: [string] 输出的商品三级类目匹配分数
    
    调用示例
    ------
    ``` python
    .extract_item_cate_match_score(
        input_item_id_list_attr="input_item_id_list",
        input_cate1_id_list_attr="input_cate1_id_list",
        input_cate2_id_list_attr="input_cate2_id_list",
        input_cate3_id_list_attr="input_cate3_id_list",
        cart_item_id_list_attr="cart_item_id_list",
        cart_item_cate1_id_list_attr="cart_item_cate1_id_list",
        cart_item_cate2_id_list_attr="cart_item_cate2_id_list",
        cart_item_cate3_id_list_attr="cart_item_cate3_id_list",
        cart_item_is_interpreting_list_attr="cart_item_is_interpreting_list",
        item_match_weight=10.0,
        cate1_match_weight=0.01,
        cate2_match_weight=0.1,
        cate3_match_weight=1.0,
        output_live_match_score_attr="live_match_score",
        output_item_match_score_attr="item_match_score",
        output_cate1_match_score_attr="cate1_match_score",
        output_cate2_match_score_attr="cate2_match_score",
        output_cate3_match_score_attr="cate3_match_score",
    )
    ```
    """
    self._add_processor(MerchantExtractItemCateMatchScoreEnricher(kwargs))
    return self 


  def realshow_enrich_sideinfo_and_gsu_for_goods_slide(self, **kwargs):
      """
      MerchantGoodsRsSideInfoForGoodsSlideEnricher
      ------
      用于商品卡内流的丰富商品曝光colossus宽度，并使用类别gsu检索
      ------
      `input_prefix_attr`: [string] 输入前缀，默认值 "origin_"
      `restrict_page_code_attr`: [int] 限制只取某一PageCode值，默认11
      `restrict_stay_time_attr`: [int] 限制停留时长大小，默认0
      `use_item_gsu_attr`: [int] 是否做Item侧的GSU，默认0
      `use_common_gsu_attr`: [int] 是否做Common侧的GSU，默认0
      `seconds_to_lookback_attr`: [int] 防止穿越的与request_time的时间间隔，默认0
      `gsu_prefix_attr_`: [string] Item侧GSU检索结果前缀，默认"gsu_"
      `gsu_limit_num_attr`: [string] Item侧GSU检索结果集个数
      `limit_num_attr`: [int] 近期Lag从小到大排列的曝光List限制长度
      `common_gsu_prefix_attr`: [string] Common侧GSU检索结果前缀，默认"common_gsu_"
      `common_gsu_limit_num_attr`: [string] Common侧GSU检索结果集个数，默认100
      `common_gsu_cate1_match_attr_`: [bool] 是否对common侧GSU检索同1级类目，默认False
      `common_gsu_cate2_match_attr_`: [bool] 是否对common侧GSU检索同2级类目，默认False
      `common_gsu_cate3_match_attr_`: [bool] 是否对common侧GSU检索同3级类目，默认False
      `item_gsu_cate1_match_attr_`: [bool] 是否对item侧GSU检索同1级类目，默认False
      `item_gsu_cate2_match_attr_`: [bool] 是否对item侧GSU检索同2级类目，默认False
      `item_gsu_cate3_match_attr_`: [bool] 是否对item侧GSU检索同3级类目，默认False
      调用示例
      ------
      ``` python
      .realshow_enrich_sideinfo_and_gsu_for_goods_slide(
        limit_num_attr=10,
        gsu_limit_num_attr=0,
        restrict_page_code_attr=11,
        restrict_stay_time_attr=0,
        use_item_gsu_attr=0,
        use_common_gsu_attr=0,
        seconds_to_lookback_attr=150,
        input_prefix_attr="origin_",
        gsu_prefix_attr="gsu_",
        common_gsu_prefix_attr="common_gsu_",
        common_limit_num_attr=50
      )
      ```
      """
      self._add_processor(MerchantGoodsRsSideInfoForGoodsSlideEnricher(kwargs))
      return self 

  def cate2cate_good_click_v2(self, **kwargs):
    """
    MerchantGsuCate2CateGoodClickEnricher
    ------
    cate2cate 根据类目检索商品点击 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
    调用示例
    ------
    ``` python
    .cate2cate_good_click_v2(
        colossus_resp_attr="colossus_resp_click_good_item",
        limit_num_attr=50,
        filter_future_attr=True,
        if_parse_to_pb_attr=True,
    )
    ```
    """
    self._add_processor(MerchantGsuCate2CateGoodClickEnricher(kwargs))
    return self 


  def cate2cate_good_order_v2(self, **kwargs):
    """
    MerchantGsuCate2CateGoodOrderEnricher
    ------
    cate2cate 根据类目检索商品点击 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
    调用示例
    ------
    ``` python
    .cate2cate_good_order_v2(
        colossus_resp_attr="colossus_resp_order_good_item",
        limit_num_attr=40,
        filter_future_attr=True,
        if_parse_to_pb_attr=False,
    )
    ```
    """
    self._add_processor(MerchantGsuCate2CateGoodOrderEnricher(kwargs))
    return self 


  def aid2aid_good_cate_order_v2(self, **kwargs):
    """
    MerchantGsuAid2AidGoodCateOrder2Enricher
    ------
    aid2aid 根据类目检索商品购买 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
    调用示例
    ------
    ``` python
    .aid2aid_good_cate_order_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 20,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId",
      target_attr_cate1="aMainGoods90dCateL1IDList",
      target_attr_cate2="aMainGoods90dCateL2IDList",
      target_attr_cate3="aMainGoods90dCateL3IDList"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidGoodCateOrder2Enricher(kwargs))
    return self

  def aid2aid_good_cate_click_v2(self, **kwargs):
    """
    MerchantGsuAid2AidGoodCateClick2Enricher
    ------
    aid2aid 根据类目检索商品点击 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
    调用示例
    ------
    ``` python
    .aid2aid_good_cate_click_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 20,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId",
      target_attr_cate1="aMainGoods90dCateL1IDList",
      target_attr_cate2="aMainGoods90dCateL2IDList",
      target_attr_cate3="aMainGoods90dCateL3IDList"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidGoodCateClick2Enricher(kwargs))
    return self

  def aid2aid_live_cate(self, **kwargs):
    """
    MerchantGsuAid2AidLiveCateEnricher
    ------
    aid2aid 根据类目检索直播
    ------
    `limit_num`: [int] 截断数量
    `result_key_field`: [string] 从colossus中取出的aId list
    `result_cate_field`: [string] 通过gsu_fetch_embedding_server_enricher取出的aid到category特征映射
    `filter_by`: [string] 从colossus中取出的timestamp list,用于过滤
    `filter_old_second`: [bigint] 过滤掉太久的live,单位是秒,表示取最近多少秒的live.设置成小于0的值则不过滤.
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `target_attr_cate1`: [string] 检索 attr 一级类目特征,设置成空字符串表示不检索一级类目
    `target_attr_cate2`: [string] 检索 attr 二级类目特征,设置成空字符串表示不检索二级类目
    `target_attr_cate3`: [string] 检索 attr 三级类目特征,设置成空字符串表示不检索三级类目
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据,默认是true
    `side_field_config`: [dict] 检索时需要取出的side info,其中key表示输入的common attr,value表示想要取出的item side info attr
    调用示例
    ------
    ``` python
    .aid2aid_live_cate(
      limit_num=100,
      result_key_field="author_id_list",
      result_cate_field="aid_features",
      filter_by="timestamp_list",
      filter_old_second=3600*24*10,
      target_attr="aId",
      target_attr_cate1="aMainGoods90dCateL1IDList",
      target_attr_cate2="aMainGoods90dCateL2IDList",
      target_attr_cate3="aMainGoods90dCateL3IDList",
      side_field_config=dict(
        author_id_list="cate_aid_list",
        label_list="cate_label_list",
        timestamp_list="cate_timestamp_list",
        play_time_list="cate_play_time_list"
        )
      )
    ```
    """
    self._add_processor(MerchantGsuAid2AidLiveCateEnricher(kwargs))
    return self
    
  def aid2aid_good_order_v2(self, **kwargs):
    """
    MerchantGsuAid2AidGoodOrder2Enricher
    ------
    aid2aid 检索商品购买 colossus v2
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `limit_num`: [int] 截断数量
    `target_attr`: [string] 检索 attr name, 一般是 aId
    `filter_future_attr`: [bool] 是否过滤可能穿越的数据
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb

    调用示例
    ------
    ``` python
    .aid2aid_good_order_v2(
      colossus_resp_attr = "colossus_resp",
      limit_num = 20,
      filter_future_attr=True,
      if_parse_to_pb_attr=False,
      target_attr = "aId"
    )
    ```
    """
    self._add_processor(MerchantGsuAid2AidGoodOrder2Enricher(kwargs))
    return self 
  
  def retrive_by_semantic(self, **kwargs):
    """
    ShopCustomerSemanticRetriever
    ------
    通过语义召回答案

    参数配置
    ------
    `kess_service`: [string] [动态参数] MmuBatchTokenizeEnricher 服务的 kess 服务名

    `timeout_ms`: [int] 请求远程服务的超时时间，默认值为 300

    `service_shard`: [string] kess 服务的 shard，默认值是 s0

    `service_group`: [string] 预估服务的 kess 服务组，默认值为 "PRODUCTION"

    `query`: [string] [动态参数] request 携带的 query

    `intent`: [string] intent 的 item_attr 名字，表示问题的相似问

    `question`: [string] question 的 item_attr 名字，表示问题的标准问

    `answer`: [string] answer 的 item_attr 名字，表示问题的回答

    `tenant_id`: [string] tenant_id 的 item_attr 名字， 表示商户 id

    `knowledge_id`: [string] knowledge_id 的 item_attr 名字， 表示商户 id

    `recall_num`: [int] 召回最大条数， 默认值10

    `reason`: [int] 召回原因


    调用示例
    ------
    ``` python
    .retrive_by_semantic(
      kess_service = "grpc_faq_search_service",
      timeout_ms = 1000,
      service_group = "PRODUCTION",
      service_shard = "s0",
      query="query",
      intent="intent",
      question="question",
      answer="answer",
      tenant_id="tenant_id",
      knowledge_id="knowledge_id",
      intent_id="intent_id",
      question_id="question_id",
      answer_id="answer_id",
      recall_num=10,
      reason=300,
    )
    ```
    """
    self._add_processor(ShopCustomerSemanticRetriever(kwargs))
    return self
  
  def merchant_ext_buy(self, **kwargs):
    """
    MerchantExtBuy
    -----
    """
    self._add_processor(MerchantExtBuy(kwargs))
    return self 
  
  def merchant_cascading_consistence(self, **kwargs):
    """
    MerchantCascadingConsistenceEnricher
    -----
    """
    self._add_processor(MerchantCascadingConsistenceEnricher(kwargs))
    return self 
  
  def merchant_sort(self, **kwargs):
    """
    MerchantRecoScoreSortArranger
    ------
    对结果集按当前 score 或将某个 item_attr 作为 score 进行排序（从高到低）
    参数配置
    ------
    `score_from_attr`: [string] 将哪个 item_attr 的值作为 score 用于排序，默认为空（直接用当前的 score 值）

    `stable_sort`: [bool] 是否使用稳定排序，默认 False

    `partial_sort`: [bool] 是否使用 std::partial_sort ，默认 False

    `partial_num`: [int] partial_sort 时的 middle num

    `desc`: [bool] 是否降序排序，默认 True
    
    `bucket_depend_attr`: [string] 分桶内部排序所依赖的 item_attr 名字，在排序过程中仅在 item_attr 相同的 item 之间进行排序。请注意，该 item_attr 必须是 int 或者 string 类型，该模式不支持 partial_sort 排序模式！

    调用示例
    ------
    ``` python
    .merchant_sort()
    # 或指定用 emp_score 的属性值作为分数排序
    .merchant_sort(score_from_attr="emp_score")
    ```
    """
    self._add_processor(MerchantRecoScoreSortArranger(kwargs))
    return self
  
  def merchant_rerank_reset_reason(self, **kwargs):
    """
    MerchantRerankResetReasonArranger
    ------
    参数配置
    ------
    `target_reason_attr`: [list] 将 common_attr 的值作为 reason, 与 target_aid_attr 一一对应

    `target_aid_attr`: [list] 将 common_attr 的值作为 aId, 与 target_reason_attr 一一对应
    
    `aId_attr`: [int] 将 item_attr, 每个 item 的 author id 字段
    调用示例
    ------
    ``` python
    .merchant_sort()
    # 或指定用 emp_score 的属性值作为分数排序
    .merchant_rerank_reset_reason(
      target_reason_attr="last_reason",
      target_aid_attr="last_aId",
      aId_attr="aId"
    )
    ```
    """
    self._add_processor(MerchantRerankResetReasonArranger(kwargs))
    return self
  
  def merchant_split_string_list(self, **kwargs):
    """
    MerchantStringListSplitEnricher
    ------
    将 string list attr 切割成多个 string/int/double list attr

    参数配置
    ------
    `input_common_attr`: [string] 输入的 string common attr。

    `output_attr_configs`: [list]
      - `export_common_attr`: [string] 切割出来的 string, 必填
      - `parse_to_type`: [string] 切割后的 string 最终 parse 的类型,仅支持 list_int64/list_double/list_string, 必填
      - `pos_in_splitted`: [int] 此 attr 在切割后的 string list 中的位置, 位置下标从 0 开始, 必填
      - `default_value`: [string] `skip_if_missing` 为 False 时才生效，当切割后的 string list size <= pos_in_splitted 时，此 attr 默认取值, 默认为 "-1"

    `delimiters`: [string] 分割字符集合。

    `trim_spaces`: [bool] 删除切割出来的 string list 周围的空白字符，默认为 False。

    `skip_empty_tokens`: [bool] 跳过空字符串，默认为 False。

    `max_splits`: [int] 最大分割次数，当小于 0 时不生效，默认不生效。

    `skip_if_missing`: [bool] 在 单个string 不完整时是否直接跳过这个string 的 split 和后续output生成, 默认为 True

    调用示例
    ------
    ``` python
    .merchant_split_string_list(
      input_common_attr = "aid_score_list",
      output_attr_configs = [
        {
          "export_common_attr": "aid_list",
          "parse_to_type": "list_int64",
          "pos_in_splitted": 0,
          "default_value": "-1"
        },
        {
          "export_common_attr": "score_list",
          "parse_to_type": "list_double",
          "pos_in_splitted": 1,
          "default_value": "0.0"
        }],
      skip_if_missing=False,
      delimiters=","
    )
    ```
    """
    self._add_processor(MerchantStringListSplitEnricher(kwargs))
    return self
    
  def duplicate_reason_count_logger(self, **kwargs):
    """
    MerchantRecoDuplicateReasonCountLoggerObserver
    ------
    冷起粗排分桶保量截断

    参数配置
    ------
    `namespace`: [string] [动态参数] 必配项,设置 perf 上报的 namespace
    `subtag`: [string] [动态参数] 必配项,设置 perf 上报的 sub_tag
    `reason_list_item_attr`: [string] [动态参数] 必配项,保存reason list的item attr

    调用示例
    ------
    ``` python
    .duplicate_reason_count_logger(
      namespace = '',
      subtag = 'duplicate_reason_count',
      reason_list_item_attr = 'reason_list'
    )
    ```
    """
    self._add_processor(MerchantRecoDuplicateReasonCountLoggerObserver(kwargs))
    return self

  def merchant_ltv_by_colossus(self, **kwargs):
    """
    MerchantLtvByColossusEnricher
    ------
    获取 user-author 的电商交易统计特征

    参数
    ------
    `colossus_resp_attr`: [string] 必配项 colossus 用户历史打赏列表
    `author_id_attr` : [string] 必配项 用户购买商品所在商家对应的aid attr name
    `last_p_exp_tag_list`: [list[int]] 选配项 需要计算的last_p_exp_tag列表, 默认全部
    `ltv_length` : [int] 选配项 ltv统计周期, 单位s, 默认72小时

    调用示例
    ------
    ``` python
    merchant_ltv_by_colossus(
       colossus_resp_attr="colossus_resp",
       author_id_attr="aId",
       last_p_exp_tag_list=[1,2],
       ltv_length=259200
    )
    ```
    """
    self._add_processor(MerchantLtvByColossusEnricher(kwargs))
    return self  

  def merchant_listwise_seq_attr_enrich(self, **kwargs):
    """
    MerchantListwiseSeqAttrEnricher
    ------
    填充listwise序列的item attr

    参数
    ------
    `item_attrs_transform_map`: [list[dict]] 必配项 需要添加后缀的item attr列表
    `seq_item_attr_name` : [string] 必配项 输入的序列item list attr name

    调用示例
    ------
    ``` python
    merchant_listwise_seq_attr_enrich(
      item_attrs_transform_map=[{"name": "pId", "as": "pId"},],
      seq_item_attr_name="generated_variant_lists",
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(MerchantListwiseSeqAttrEnricher(kwargs))
    return self
  
  def merchant_listwise_seq_attr_v2_enrich(self, **kwargs):
    """
    MerchantListwiseSeqAttrV2Enricher
    ------
    填充listwise序列的item attr

    参数
    ------
    `int_item_attrs_map`: [list[dict]] 必配项 需要添加后缀的item attr列表
    `double_item_attrs_map` : [string] 必配项 输入的序列item list attr name

    调用示例
    ------
    ``` python
    merchant_listwise_seq_attr_v2_enrich(
      int_item_attrs_map=[{"from_item": "pId", "pack_as": "seq_pid_list"},],
      double_item_attrs_map=[{"from_item": "price", "pack_as": "seq_price_list"},],
      seq_item_attr_name="generated_variant_lists",
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(MerchantListwiseSeqAttrV2Enricher(kwargs))
    return self
  
  def merchant_listwise_new_seq_attr_v2_enrich(self, **kwargs):
    """
    MerchantListwiseNewSeqAttrV2Enricher
    ------
    填充listwise序列的item attr

    参数
    ------
    `int_item_attrs_map`: [list[dict]] 必配项 需要添加后缀的item attr列表
    `double_item_attrs_map` : [string] 必配项 输入的序列item list attr name

    调用示例
    ------
    ``` python
    merchant_listwise_new_seq_attr_v2_enrich(
      int_item_attrs_map=[{"from_item": "pId", "pack_as": "seq_pid_list"},],
      double_item_attrs_map=[{"from_item": "price", "pack_as": "seq_price_list"},],
      seq_item_attr_name="generated_variant_lists",
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(MerchantListwiseNewSeqAttrV2Enricher(kwargs))
    return self

  def merchant_listwise_generator_candidate_attr_enricher(self, **kwargs):
    """
    MerchantListwiseGeneratorCandidateAttrEnricher
    ------
    生成序列生成模型特征

    参数
    ------
    `int_item_attrs_map`: [list[dict]] 必配项 需要添加后缀的item attr列表
    `double_item_attrs_map` : [string] 必配项 输入的序列item list attr name

    调用示例
    ------
    ``` python
    merchant_listwise_generator_candidate_attr_enricher(
      int_item_attrs_map=[{"from_item": "pId", "pack_as": "seq_pid_list"},],
      double_item_attrs_map=[{"from_item": "price", "pack_as": "seq_price_list"},],
      seq_item_attr_name="generated_variant_lists",
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(MerchantListwiseGeneratorCandidateAttrEnricher(kwargs))
    return self
  
  def merchant_listwise_generator_candidate_attr_v1_enricher(self, **kwargs):
    """
    MerchantListwiseGeneratorCandidateAttrV1Enricher
    ------
    生成序列生成模型特征

    参数
    ------
    `int_item_attrs_map`: [list[dict]] 必配项 需要添加后缀的item attr列表
    `double_item_attrs_map` : [string] 必配项 输入的序列item list attr name

    调用示例
    ------
    ``` python
    merchant_listwise_generator_candidate_attr_v1_enricher(
      int_item_attrs_map=[{"from_item": "pId", "pack_as": "seq_pid_list"},],
      double_item_attrs_map=[{"from_item": "price", "pack_as": "seq_price_list"},],
      seq_item_attr_name="generated_variant_lists",
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(MerchantListwiseGeneratorCandidateAttrV1Enricher(kwargs))
    return self

  def listwise_unify_bonus_enricher(self, **kwargs):
    """
    ListwiseUnifyBonusEnricher
    ------
    填充listwise序列的item attr

    参数
    ------
    `mix_bonus_weight` : list[dict] 除了name 其他的全部为参数传递
    `mix_ctr_list mix_cvr_list` 是否用mix xtpr 做bonus的一部分，必须传10个，按照顺序传
    `seq_item_attr_name` : 序列list

    调用示例
    ------
    ``` python
    listwise_unify_bonus_enricher(
      mix_bonus_weight=[{"name": "bonus_name", "bonus_weight": {{weight}},"mix_cvr_exponent":{{exponent}},"mix_ctr_exponent":{{exponent}},""price_exponent":{{exponent}}"}],
      seq_item_attr_name="generated_variant_lists",
      mix_ctr_list=["ctr1","ctr2"],需要保证顺序即 1 2 3 4 .. 9的传入顺序 
      mix_cvr_list=["cvr1","cvr2"],同上
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(ListwiseUnifyBonusEnricher(kwargs))
    return self
  
  def listwise_unify_bonus_v2_enricher(self, **kwargs):
    """
    ListwiseUnifyBonusV2Enricher
    ------
    填充listwise序列的item attr

    参数
    ------
    `mix_bonus_weight` : list[dict] 除了name 其他的全部为参数传递
    `mix_ctr_list mix_cvr_list` 是否用mix xtpr 做bonus的一部分，必须传10个，按照顺序传
    `seq_item_attr_name` : 序列list

    调用示例
    ------
    ``` python
    listwise_unify_bonus_enricher(
      mix_bonus_weight=[{"name": "bonus_name", "bonus_weight": {{weight}},"mix_cvr_exponent":{{exponent}},"mix_ctr_exponent":{{exponent}},""price_exponent":{{exponent}}"}],
      seq_item_attr_name="generated_variant_lists",
      mix_ctr_list=["ctr1","ctr2"],需要保证顺序即 1 2 3 4 .. 9的传入顺序 
      mix_cvr_list=["cvr1","cvr2"],同上
      target_item={
          "item_type": 2,
          "reason": 1,
      },
    )
    ```
    """
    self._add_processor(ListwiseUnifyBonusV2Enricher(kwargs))
    return self

  def merchant_business_flag_enricher(self, **kwargs):
    """
    MerchantBusinessFlagEnricher
    ------
    填充商品业务flag位

    参数
    ------
    `output_attr_name`: [string] 生成的flag字段名称
    `business_flag_map_list`: list[dict] 业务attr以及对应的flag位

    调用示例
    ------
    ``` python
    merchant_business_flag_enricher(
      business_flag_map_list=[
        {"name": "iGoodsMiaoshaForRecoPindao", "flag": 1},
        {"name": "iGoodsSuperLink", "flag": 2},
        {"name": "cold_start_flag", "flag": 4},
      ],
      output_attr_name="merchant_business_flag",
    )
    ```
    """
    self._add_processor(MerchantBusinessFlagEnricher(kwargs))
    return self
  
  def merchant_listwise_sequence_pack_enricher(self, **kwargs):
    """
    MerchantListwiseSequencePackEnricher
    ------
    序列属性 pack

    参数
    ------
    `sequence_attr_name`: [string] 序列属性名
    `mappings`: list[dict] pack属性映射表

    调用示例
    ------
    ``` python
    merchant_listwise_sequence_pack_enricher(
      mappings=[
        {"from_item": "item_id", "to_common": "pack_item_id_list},
        {"from_item": "business_flag", "to_common": "pack_business_flag_list"},
      ],
      sequence_attr_name="generated_variant_lists",
    )
    ```
    """
    self._add_processor(MerchantListwiseSequencePackEnricher(kwargs))
    return self

  def merchant_gen_ensemble_seed_sequence(self, **kwargs):
    """
    MerchantGenEnsembleSeedSequenceEnricher
    ------
    生成listwise种子序列

    参数
    ------
    `queues`: [string] ensemble队列
    `return_item_type` : [string] 选配项 序列的item_type,默认为1
    `sequence_max_size`: [int] 选配项 序列长度, 默认6
    `max_sequence_num` : [string] 种子序列个数，默认50

    示例
    ------
    ``` python
    merchant_gen_ensemble_seed_sequence(
       queues=[],
       return_item_type=1,
       sequence_max_size=6,
       max_sequence_num=100
    )
    ```
    """
    self._add_processor(MerchantGenEnsembleSeedSequenceEnricher(kwargs))
    return self

  def merchant_live_gsu_index(self, **kwargs):
    """
    MerchantLiveGsuIndexEnricher
    ------
    根据 photo 所属的 aid 进行 gsu 搜索, 抽取 colossus 所有 photo 的 sign/slot 特征作为 common attr， 并返回每个 target photo 通过 aid2aid 检索得到的 index。

    参数
    ------
    `output_index_common_attr`: [string] concat 后的 index 输出 common attr name

    `output_index_item_attr`: [string] index 输出 item attr name

    `output_sign_attr`: [string] sign 输出 common attr name

    `output_slot_attr`: [string] slot 输出 common attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `colossus_limit_num_attr`: [int] colossus 限定长度数量

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_attr`: [string] item侧使用的 aid 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    示例
    ------
    ``` python
    .merchant_live_gsu_index(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      output_index_common_attr='aid2aid_index_common',
                      output_index_item_attr='aid2aid_index_item',
                      limit_num_attr='live_limit_num',
                      colossus_limit_num_attr=5000,
                      target_attr = 'aId',
                      slots_id=[1000, 1001, 1002, 1003, 1004],
                      mio_slots_id=[1000, 1001, 1002, 1003, 1004]
                      )
    ```
    """
    self._add_processor(MerchantLiveGsuIndexEnricher(kwargs))
    return self

  def merchant_live_gsu_common(self, **kwargs):
    """
    MerchantLiveGsuIndexEnricher
    ------
    抽取 colossus 所有 photo 的 sign/slot 特征作为 common attr

    参数
    ------
    `output_sign_attr`: [string] sign 输出 common attr name

    `output_slot_attr`: [string] slot 输出 common attr name
   
    `output_colossus_item_key_attr`: [string] aid list 输出 common attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `colossus_limit_num_attr`: [int] colossus 限定长度数量

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    示例
    ------
    ``` python
    .merchant_live_gsu_common(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      colossus_limit_num_attr=5000,
                      slots_id=[1000, 1001, 1002, 1003, 1004],
                      mio_slots_id=[1000, 1001, 1002, 1003, 1004]
                      )
    ```
    """
    self._add_processor(MerchantLiveGsuCommonEnricher(kwargs))
    return self
  
  def merchant_mix_rank_gen_seed_sequence(self, **kwargs):
    """
    MerchantMixRankGenSeedSequenceEnricher
    ------
    生成listwise种子序列

    参数
    ------
    `queues`: [string] ensemble队列
    `return_item_type` : [string] 选配项 序列的item_type,默认为1
    `sequence_max_size`: [int] 选配项 序列长度, 默认6
    `max_sequence_num` : [string] 种子序列个数，默认50
    示例
    ------
    ``` python
    merchant_gen_ensemble_seed_sequence(
       queues=[],
       return_item_type=1,
       sequence_max_size=6,
       max_sequence_num=100
    )
    ```
    """
    self._add_processor(MerchantMixRankGenSeedSequenceEnricher(kwargs))
    return self
  
  def merchant_mix_rank_gen_seed_sequence_v1(self, **kwargs):
    """
    MerchantMixRankGenSeedSequenceV1Enricher
    ------
    生成listwise种子序列

    参数
    ------
    `queues`: [string] ensemble队列
    `return_item_type` : [string] 选配项 序列的item_type,默认为1
    `sequence_max_size`: [int] 选配项 序列长度, 默认6
    `max_sequence_num` : [string] 种子序列个数，默认50
    示例
    ------
    ``` python
    merchant_gen_ensemble_seed_sequence_v1(
       queues=[],
       return_item_type=1,
       sequence_max_size=6,
       max_sequence_num=100
    )
    ```
    """
    self._add_processor(MerchantMixRankGenSeedSequenceV1Enricher(kwargs))
    return self
  
  def merchant_mix_rank_new_mix_generator_enricher(self, **kwargs):
    """
    MerchantMixRankNewMixGeneratorEnricher
    ------
    生成listwise种子序列

    参数
    ------
    `queues`: [string] ensemble队列
    `return_item_type` : [string] 选配项 序列的item_type,默认为1
    `sequence_max_size`: [int] 选配项 序列长度, 默认6
    `max_sequence_num` : [string] 种子序列个数，默认50
    示例
    ------
    ``` python
    merchant_mix_rank_new_mix_generator_enricher(
       queues=[],
       return_item_type=1,
       sequence_max_size=6,
       max_sequence_num=100
    )
    ```
    """
    self._add_processor(MerchantMixRankNewMixGeneratorEnricher(kwargs))
    return self
  
  def merchant_embd_sim_variant_enricher(self, **kwargs):
    """
    MerchantEmbdSimVariantEnricher
    ------
    商城embedding相似度打散

    参数
    ------
    `window_size`: [int] 打散窗口
    `default_threshold`: [double] 相似度阈值, 默认0.5
    `max_satisfied_pick`: [int] 满足打散条件的序列长度, 默认10
    `dimension`: [int] dim
    `attr_name`: [string] embedding字段
    `prevs_attr_name`: [string] 前序刷次embedding字段
    `output_attr_name`: [string] 输出attr字段
    示例
    ------
    ``` python
    merchant_embd_sim_variant_enricher(
       window_size=4,
       default_threshold=0.8,
       max_satisfied_pick=20,
       attr_name="item_embd",
       prevs_attr_name="prevs_embedding",
       output_attr_name="embd_variant_pos"
    )
    ```
    """
    self._add_processor(MerchantEmbdSimVariantEnricher(kwargs))
    return self

  def listwise_sequence_dedup_enricher(self, **kwargs):
    """
    ListwiseSequeceDedupEnricher
    ------
    序列去重

    参数
    ------
    `seq_item_attr_name`:[string] 序列名
    `max_sequence_num` : [int] 序列个数
    `uniq_id_weight`: [int] 距离权重
    示例
    ------
    ``` python
    listwise_sequence_dedup_enricher(
       seq_item_attr_name="generated_variant_lists_uni",
       max_sequence_num=75,
       uniq_id_weight=2,
    )
    ```
    """
    self._add_processor(ListwiseSequeceDedupEnricher(kwargs))
    return self
  def merchant_mix_rank_gen_seed_sequence_v2(self, **kwargs):
    """
    MerchantMixRankGenSeedSequenceV2Enricher
    ------
    生成listwise种子序列

    参数
    ------
    `queues`: [string] ensemble队列
    `return_item_type` : [string] 选配项 序列的item_type,默认为1
    `sequence_max_size`: [int] 选配项 序列长度, 默认6
    `max_sequence_num` : [string] 种子序列个数，默认50
    示例
    ------
    ``` python
    merchant_gen_ensemble_seed_sequence_v2(
       queues=[],
       return_item_type=1,
       sequence_max_size=6,
       max_sequence_num=100
    )
    ```
    """
    self._add_processor(MerchantMixRankGenSeedSequenceV2Enricher(kwargs))
    return self

  def merchant_mix_rank_gen_permutations_seed_sequence(self, **kwargs):
    """
    MerchantMixRankGenPermutationsSeedSequenceEnricher
    ------
    生成listwise种子序列

    参数
    ------
    `queues`: [string] ensemble队列
    `return_item_type` : [string] 选配项 序列的item_type,默认为1
    `sequence_max_size`: [int] 选配项 序列长度, 默认6
    `max_sequence_num` : [string] 种子序列个数，默认50
    示例
    ------
    ``` python
    merchant_gen_ensemble_seed_sequence(
       queues=[],
       return_item_type=1,
       sequence_max_size=6,
       max_sequence_num=100
    )
    ```
    """
    self._add_processor(MerchantMixRankGenPermutationsSeedSequenceEnricher(kwargs))
    return self

  def merchant_mix_rank_gen_egpm_with_guassion_perm_sequence_enricher(self, **kwargs):
        """
        MerchantMixRankGenEgpmWithGuassionPermSequenceEnricher
        ------
        生成listwise种子序列

        参数
        ------
        `queues`: [string] ensemble队列
        `return_item_type` : [string] 选配项 序列的item_type,默认为1
        `sequence_max_size`: [int] 选配项 序列长度, 默认6
        `max_sequence_num` : [string] 种子序列个数，默认50
        示例
        ------
        ``` python
        merchant_gen_ensemble_seed_sequence(
           queues=[],
           return_item_type=1,
           sequence_max_size=6,
           max_sequence_num=100
        )
        ```
        """
        self._add_processor(
            MerchantMixRankGenEgpmWithGuassionPermSequenceEnricher(kwargs)
        )
        return self

  def merchant_mix_rank_gen_seed_sequence_beam_search_enricher(self, **kwargs):
        """
        MerchantMixRankGenSeedSequenceBeamSearchEnricher
        ------
        生成listwise种子序列

        参数
        ------
        `queues`: [string] ensemble队列
        `return_item_type` : [string] 选配项 序列的item_type,默认为1
        `sequence_max_size`: [int] 选配项 序列长度, 默认6
        `max_sequence_num` : [string] 种子序列个数，默认50
        示例
        ------
        ``` python
        merchant_gen_ensemble_seed_sequence(
           queues=[],
           return_item_type=1,
           sequence_max_size=6,
           max_sequence_num=100
        )
        ```
        """
        self._add_processor(
            MerchantMixRankGenSeedSequenceBeamSearchEnricher(kwargs)
        )
        return self

  def merchant_mix_rank_gen_seed_sequence_topk_sampling_enricher(self, **kwargs):
        """
        MerchantMixRankGenSeedSequenceTopkSamplingEnricher
        ------
        生成listwise种子序列

        参数
        ------
        `queues`: [string] ensemble队列
        `return_item_type` : [string] 选配项 序列的item_type,默认为1
        `sequence_max_size`: [int] 选配项 序列长度, 默认6
        `max_sequence_num` : [string] 种子序列个数，默认50
        示例
        ------
        ``` python
        merchant_gen_ensemble_seed_sequence(
           queues=[],
           return_item_type=1,
           sequence_max_size=6,
           max_sequence_num=100
        )
        ```
        """
        self._add_processor(
            MerchantMixRankGenSeedSequenceTopkSamplingEnricher(kwargs)
        )
        return self

  def merchant_mix_rank_gen_seed_sequence_weight_range_enricher(self, **kwargs):
        """
        MerchantMixRankGenSeedSequenceWeightRangeEnricher
        ------
        生成listwise种子序列

        参数
        ------
        `queues`: [string] ensemble队列
        `return_item_type` : [string] 选配项 序列的item_type,默认为1
        `sequence_max_size`: [int] 选配项 序列长度, 默认6
        `max_sequence_num` : [string] 种子序列个数，默认50
        示例
        ------
        ``` python
        merchant_gen_ensemble_seed_sequence(
           queues=[],
           return_item_type=1,
           sequence_max_size=6,
           max_sequence_num=100
        )
        ```
        """
        self._add_processor(
            MerchantMixRankGenSeedSequenceWeightRangeEnricher(kwargs)
        )
        return self

  def merchant_mix_rank_gen_seed_sequence_model_enricher(self, **kwargs):
        """
        MerchantMixRankGenSeedSequenceModelEnricher
        ------
        模型化序列生成

        参数
        ------
        `queues`: [string] ensemble队列
        `return_item_type` : [string] 选配项 序列的item_type,默认为1
        `sequence_max_size`: [int] 选配项 序列长度, 默认6
        `max_sequence_num` : [string] 种子序列个数，默认50
        示例
        ------
        ``` python
        merchant_mix_rank_gen_seed_sequence_model_enricher(
           queues=[],
           return_item_type=1,
           sequence_max_size=6,
           max_sequence_num=100
        )
        ```
        """
        self._add_processor(
            MerchantMixRankGenSeedSequenceModelEnricher(kwargs)
        )
        return self

  def merchant_live_gsu_with_cluster_index(self, **kwargs):
    """
    MerchantLiveGsuWithClusterIndexEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_index_common_attr`: [string] concat 后的 index 输出 common attr name

    `output_index_item_attr`: [string] index 输出 item attr name

    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `output_target_attr`: [string] target 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr, colossus_resp的proto是LiveItemV2

    `limit_num_attr`: [int] 返回数目 attr

    `colossus_limit_num`: [int] colossus 抽取限制

    `slots_ids` : [int] 抽sign 使用的slot

    `mio_slots_ids` : [int] mio 前向使用的slot

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `use_padding`: [bool] 输出的 slot 是否默认 padding 补齐 -1 到 limit_num

    `gift_item_only`: [bool] 是否只输出打赏过的 item，default:false

    示例
    ------
    ``` python
    .merchant_live_gsu_with_cluster_index(colossus_resp_attr='live_colossus_resp',
                      output_sign_attr='live_gsu_sign',
                      output_slot_attr='live_gsu_slot',
                      output_index_common_attr='aid2aid_index_common',
                      output_index_item_attr='aid2aid_index_item',
                      limit_num_attr='live_limit_num',
                      colossus_limit_num_attr=5000,
                      target_cluster_attr = 'lHetuCoverEmbeddingCluster',
                      )
    ```
    """
    self._add_processor(MerchantLiveGsuWithClusterIndexEnricher(kwargs))
    return self
  
  def merchant_gen_original_sequence(self, **kwargs):
    """
    MerchantGenOriginalSequenceEnricher
    ------
    生成listwise序列

    参数
    ------
    `reason`: [string] 序列的reason, 默认为1
    `item_type` : [string] 选配项 序列的item_type,默认为2
    `sequence_length`: [int] 选配项 序列长度, 默认6
    `output_sequence_attr_name` : [string] 选配项 输出的序列item list attr name

    调用示例
    ------
    ``` python
    merchant_gen_original_sequence(
       reason=1,
       item_type=2,
       sequence_length=6,
       output_sequence_attr_name="generated_variant_lists"
    )
    ```
    """
    self._add_processor(MerchantGenOriginalSequenceEnricher(kwargs))
    return self

  def merchant_gen_prm_sequence_enricher(self, **kwargs):
    """
    MerchantGenPRMSequenceEnricher
    ------
    将prm listwise序列中的item预估score赋值给原来的item

    参数
    ------
    `prm_score_attrs`: [list[string]] 选配项 listwise序列中的item预估值的attr name
    `item_type` : [int] 选配项 序列的item_type,默认为2
    `reason`: [int] 选配项 序列的reason, 默认为1
    `input_sequence_attr_name` : [string] 选配项 输入的序列item list attr name
    `output_score_attr_name` : [string] 选配项 输出的序列item list attr nam

    调用示例
    ------
    ``` python
    merchant_gen_prm_sequence_enricher(
        prm_score_attrs=[
          "rerank_pos0",
          "rerank_pos1",
          "rerank_pos2",
          "rerank_pos3",
          "rerank_pos4",
          "rerank_pos5",
        ],
        item_type=2,
        reason=1,
        input_sequence_attr_name="generated_variant_lists",
        output_sequence_attr_name="generated_variant_lists",
        output_score_attr_name="prm_score",
    )
    ```
    """
    self._add_processor(MerchantGenPRMSequenceEnricher(kwargs))
    return self
  
  def merchant_gen_prm_sequence_v2_enricher(self, **kwargs):
    """
    MerchantGenPRMSequenceV2Enricher
    ------
    将prm listwise序列中的item预估score赋值给原来的item

    参数
    ------
    `prm_score_mappings`: [list[dict]] 选配项 listwise序列中的item预估值的attr name
    `list_score_mappings` : [list[dict]] 选配项 listwise序列打分的attr name
    `input_sequence_attr_name` : [string] 选配项 输入的序列结果attr name
    `output_sequence_index_attr_name` : [string] 选配项 输出的序列位置attr name

    调用示例
    ------
    ``` python
    merchant_gen_prm_sequence_v2_enricher(
        prm_score_mappings=[
          {
            "from_attrs": [
              "pos{i}_ctr".format(i) for i in range(10)
            ],
            "to_attr": "rerank_pos_ctr"
          },
          {
            "from_attrs": [
              "pos{i}_cvr".format(i) for i in range(10)
            ],
            "to_attr": "rerank_pos_cvr"
          }
        ],
        list_score_mappings=[
          {
            "from_attr": "rerank_context",
            "to_attr": "rerank_context_score"
          },
          {
            "from_attr": "rerank_context_ori",
            "to_attr": "rerank_context_score_ori"
          }
        ],
        input_sequence_attr_name="generated_variant_lists",
        output_sequence_index_attr_name="final_sequence_index"
    )
    ```
    """
    self._add_processor(MerchantGenPRMSequenceV2Enricher(kwargs))
    return self

  def merchant_multi_sort_post(self, **kwargs):
    """
    MerchantMultiSortPostEnricher 
    ------
    multi query sort gsu for GoodClickColossus(V2)
    参数
    ------
    `common_distance_ptr_attr` : [string] 记录多query检索后返回的distance，flatten 

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    调用示例
    ------
    ``` python
    .merchant_multi_sort_post(
        common_distance_ptr_attr="mmu_distance",
        channel_size=2,
        target_item={"is_target_goods_embedding_hit": 1},
        colossus_pid_attr="colossus_goods_itemid",
        author_id_attr="item_click_author_id",
        timestamp_attr="item_click_time_stamp",
        output_sign_attr="multi_goods_gsu_signs",
        output_slot_attr="multi_goods_gsu_slots",
    )
    ```
    """
    self._add_processor(MerchantMultiSortPostEnricher(kwargs))
    return self

  def merchant_ltv_watch_enricher(self, **kwargs):
    """
    MerchantMultiSortPostEnricher 
    """
    self._add_processor(MerchantLtvWatchEnricher(kwargs))
    return self

  def merchant_ltv_cart_enricher(self, **kwargs):
    """
    MerchantMultiSortPostEnricher 
    """
    self._add_processor(MerchantLtvCartEnricher(kwargs))
    return self
  

  def merchant_gsu_with_index_merchant_video(self, **kwargs):
    """
    MerchantGsuWithIndexMerchantVideoEnricher
    只适配电商视频colossus
    ------
    根据 common_attr中的 item index, 从 `colossus_pid_attr` 中选择对应的 history item，和
    target item 一起计算对应的 sign, 返回 output_sign 和 output_slots

    参数
    ------
    `sorted_item_idx_attr` : [string] 排序后 top_n item index 对应的 attr

    `sorted_item_pxtrs_attr` : [string] 排序后 top_n item 打分 对应的 attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `category_attr` : [string] category attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `field1_attr`-`field8_attr`，必填，见示例

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    `output_item_colossus_pid_attr` : [string] 与上一个对应的colossus_pid列表中的内容，为空时不返回

    `output_item_colossus_pxtrs_attr` :  [string] 与上一个对应的distance分数，为空时不返回

    示例
    ------
    ``` python
        .merchant_gsu_with_index_merchant_video(
          colossus_pid_attr ='photo_colossus_photo_id_list',
          timestamp_attr="photo_colossus_timestamp",
          category_attr="photo_colossus_category",
          field1_attr="photo_colossus_photo_id",
          field2_attr="photo_colossus_author_id",
          field3_attr="photo_colossus_item_id",
          field4_attr="photo_colossus_duration",
          field5_attr="photo_colossus_play_time",
          field6_attr="photo_colossus_spu_id",
          field7_attr="photo_colossus_channel",
          field8_attr="photo_colossus_label",
          output_sign_attr = "gsu_signs",
          output_slot_attr = "gsu_slots",
          sorted_item_idx_attr = "sorted_item_index",
          sorted_item_pxtrs_attr = "pxtrs_test",
          mio_slots_id=[500,501,502,503,504,505,506,507,508,509,510,511,512,513,514],
          slots_id=[500,501,502,503,504,505,506,507,508,509,510,511,512,513,514],
          top_n=50,
          output_item_colossus_pid_attr = 'output_pid',
          output_item_colossus_pxtrs_attr = 'output_scores'
    )
    ```
    """
    self._add_processor(MerchantGsuWithIndexMerchantVideoEnricher(kwargs))
    return self

  
  def merchant_ltv_slice_enricher(self, **kwargs):
    """
    MerchantLtvSliceEnricher 
    """
    self._add_processor(MerchantLtvSliceEnricher(kwargs))
    return self
  
  def merchant_ltv_retarget_enricher(self, **kwargs):
    """
    MerchantLtvRetargetEnricher 
    """
    self._add_processor(MerchantLtvRetargetEnricher(kwargs))
    return self

  def get_label_from_rodis(self, **kwargs):
    """
    MerchantCommonRodisEnricher
    ------
    将指定uId中的label对应的rodis中的data，返回供之后取用
    payload id 配置参考：https://git.corp.kuaishou.com/ksib-reco/ksib-reco/-/blob/master/ksib-reco-proto/src/main/proto/ksib/reco/common/rodis_payload.proto 里面的 enum KwaiActionTypePayloadId
    参数配置
    ------

    `domain`: [string] Rodis 数据的 domain

    `kess_service`: [string] Rodis 服务的 kess 名称

    `service_group`: [string] Rodis 服务的 kess 服务组，默认值为 "PRODUCTION"

    `timeout_ms`: [int] 超时时间，默认值为 50

    `payload_desc`: [object] 对应 kuaishou.ds.PayloadDescriptor
      - `payload_id`: [int] 获取 label 用的 payload id
      - `len_limit`: [int] [动态参数] 设置 label 的长度，默认最长10000
      - `after_ts`: [int] [动态参数] 选配项，label 时间范围的上限
      - `before_ts`: [int] [动态参数] 选配项，label 时间范围的下限
    
    `output_common_attr` : [string] pid对应的output common attr name
 
    调用示例
    ------
    ``` python
    .get_label_from_rodis(
      domain="KSIB_UID",
      kess_service="grpc_rodisKsib",
      payload_desc = {
        "payload_id": 2,
        "len_limit": 2,
      }
      output_common_attr = "merchant_labels"
    )
    ```
    """
    self._add_processor(MerchantCommonRodisEnricher(kwargs))
    return self

  def merchant_ltv_by_rodis(self, **kwargs):
    """
    MerchantLtvByRodisEnricher
    ------
    获取 user-author 的电商交易统计特征

    参数
    ------
    `rodis_resp_attr`: [string] 必配项 colossus 用户历史打赏列表
    `author_id_attr` : [string] 必配项 用户购买商品所在商家对应的aid attr name
    `last_p_exp_tag_list`: [list[int]] 选配项 需要计算的last_p_exp_tag列表, 默认全部
    `ltv_length` : [int] 选配项 ltv统计周期, 单位s, 默认72小时

    示例
    ------
    ``` python
    merchant_ltv_by_rodis(
       rodis_resp_attr="rodis_resp",
       author_id_attr="aId",
       last_p_exp_tag_list=[1,2],
       ltv_length=259200
    )
    ```
    """
    self._add_processor(MerchantLtvByRodisEnricher(kwargs))
    return self
  
  def merchant_gsu_photo_cluster_parse_pb(self, **kwargs):
    """
    MerchantGsuPhotoClusterParsePbEnricher
    ------
    根据 photo 所属的 cluster 进行 gsu 搜索并填充相似视频的 sign/slot 特征

    参数
    ------
    `output_sign_attr`: [string] sign 输出 attr name

    `output_slot_attr`: [string] slot 输出 attr name

    `colossus_resp_attr`: [string] colossus_resp 输入的 attr

    `target_cluster_attr`: [string] item侧使用的 item cluster 属性

    `limit_num_attr`: [int] 返回数目 attr

    `timeout_ms`: [int] 查询的超时时间，默认为 10 ms

    `kess_service`: [string] embedding_server 的 kess 服务名

    `shards`: [int] embedding_server 的 shard 数，pid 会对 shards 取模后分发到各个 shard 进行查询

    `kess_cluster`: [string] embedding_server 的 kess 集群名，默认为 PRODUCTION

    `max_pids_per_request`: [int] 单次 rpc 请求中的最大 pid 个数，为 0 代表不限制

    `use_append_cluster`  : [bool] 是否使用扩增类别, 默认为 False
    
    `hetu_1w_cluster_json_value`: [string] hetu 1w cluster string 值
    
    `if_parse_to_pb_attr`: [bool] 是否使用parse_to_pb
    
    `slot_as_attr_name`: [bool] 是否slot_as_attr_name

    示例
    ------
    ``` python
    .MerchantGsuPhotoClusterParsePbEnricher(colossus_resp_attr='colossus_resp',
                        output_sign_attr='sign',
                        output_slot_attr='slot',
                        limit_num_attr='limit_num',
                        target_cluster_attr = 'sEshopLive2mSliceUniSpaceClusterId',
                        cluster_id_service_type='embedding_server',
                        kess_service='mmu_hetu_cluster_id_query_server',
                        shards=1,
                        use_append_cluster = false,
                        if_parse_to_pb_attr = False,
                        slot_as_attr_name = True,
                        hetu_1w_cluster_json_value="")
    ```
    """
    self._add_processor(MerchantGsuPhotoClusterParsePbEnricher(kwargs))
    return self

  def merchant_gsu_photo_cluster(self, **kwargs):
    self._add_processor(MerchantGsuPhotoClusterEnricher(kwargs))
    return self
  
  def merchant_gsu_aid_enricher(self, **kwargs):
    self._add_processor(MerchantGsuAidEnricher(kwargs))
    return self

  def merchant_cold_start_gen_bid_message_enricher(self, **kwargs):
    """
    MerchantColdStartGenBidMessageEnricher
    ------
    以 json_string 格式返回冷启bid Server 所需的 common, item 信息

    参数
    ------
    `input_common_attr_info`: [string] 必配项 Common 侧信息
    `input_item_attr_info` : [string] 必配项 item 侧信息
    `output_common_json`: [list[int]] 必配项 返回的 common_attr

    示例
    ------
    ``` python
    merchant_cold_start_gen_bid_message_enricher(
       input_common_attr_info=["uid", "llsid", "exp_name"],
       input_item_attr_info=["aid", "live_id", "score"],
       output_common_json= "erchant_cold_start_gen_bid_message"
    )
    ```
    """
    self._add_processor(MerchantColdStartGenBidMessageEnricher(kwargs))
    return self
  
  def merchant_gsu_retarget_enricher(self, **kwargs):
    self._add_processor(MerchantGsuRetargetEnricher(kwargs))
    return self

  def merchant_cate_retarget_enricher(self, **kwargs):
    self._add_processor(MerchantCateRetargetEnricher(kwargs))
    return self
  
  def merchant_ltv_by_cate(self, **kwargs):
    self._add_processor(MerchantLtvByCateEnricher(kwargs))
    return self

  def merchant_user_ltv(self, **kwargs):
    self._add_processor(MerchantUserLtvEnricher(kwargs))
    return self
 
  def merchant_ltv_by_ext(self, **kwargs):
    self._add_processor(MerchantLtvByExtEnricher(kwargs))
    return self

  def merchant_live_gsu_tower_sort_post(self, **kwargs):
    """
    MerchantLiveGsuTowerSortPostEnricher
    ------
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_pid_attr` : [string] 取自colossus的pid列表

    `author_id_attr` : [string] pid对应的aid attr name

    `tag_attr` : [string] pid对应的 tag attr name

    `optag_attr` : [string] pid对应的 optag attr name

    `play_time_attr` : [string] pid对应的 play time attr name

    `page_type_attr` : [string] pid对应的 page_type attr name

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    示例
    ------
    ``` python
    .merchant_live_gsu_tower_sort_post(
      common_distance_ptr_attr="live_distance",
      target_item={"is_target_photo_embedding_hit": 1},
      colossus_pid_attr="live_colossus_pid",
      author_id_attr="live_colossus_aid",
      tag_attr="live_colossus_tag",
      optag_attr="live_colossus_optag",
      play_time_attr="live_colossus_play_time",
      page_type_attr="live_colossus_page_type",
      timestamp_attr="live_colossus_timestamp",
      output_sign_attr="live_gsu_signs",
      output_slot_attr="live_gsu_slots",
      slots_id=[26, 128, 375, 373 , 374, 376, 377],
      mio_slots_id=[371, 372, 375, 373, 374, 376, 377],
      top_n=50)
    ```
    """
    self._add_processor(MerchantLiveGsuTowerSortPostEnricher(kwargs))
    return self

  def extract_merchant_colossus_click_slice_feature_v2(self,**kwargs):
    """
    MerchantColossusClickSliceFeatureV2Enricher
    ------
    merchant click colossusV2 slice fg（FeatureServer列取算子 和V1逻辑上保持一致）

    参数配置
    ------

    `item_id_list_attr`: [string] 从给定 common attr 获取 colossus item_id_list
    
    `click_timestamp_list_attr`: [string] 从给定 common attr 获取 colossus click_timestamp_list
    
    `category_list_attr`: [string] 从给定 common attr 获取 colossus category_list
    
    `leaf_category_list_attr`: [string] 从给定 common attr 获取 colossus leaf_category_list
    
    `seller_id_list_attr`: [string] 从给定 common attr 获取 colossus seller_id_list_attr
    
    `cart_truncat_num`: [int] 从给定 common attr 获取 小黄车列表截断的数目。

    `target_aid`: [string] 从给定 item attr 获取 aid属性。

    `target_cate1`: [string] 从给定 item attr 获取 小黄车列表一级类目。

    `target_cate2`: [string] 从给定 item attr 获取 小黄车列表二级类目。

    `target_cate3`: [string] 从给定 item attr 获取 小黄车列表三级类目。

    `target_leaf`: [string] 从给定 item attr 获取 小黄车列表叶子类目。

    `print_debug_log`: [bool] 是否打印 debug 信息到 stdout，用于离线调试，默认 False。

    调用示例
    ------
    ``` python
    .extract_merchant_colossus_click_slice_feature_v2(
        colossus_resp_attr='item_id_list_attr',
        click_timestamp_list_attr='click_timestamp_list_attr',
        category_list_attr='category_list_attr',
        leaf_category_list_attr = leaf_category_list_attr',
        seller_id_list_attr = 'seller_id_list_attr',
        target_aid='target_aid',
        target_cate1 ='target_cate1',
        target_cate2 ='target_cate2',
        target_cate3 ='target_cate3',
        target_leaf ='target_leaf',
        print_debug_log = True,
        filter_time = 5,
        cart_truncat_num = 10
        )
    ```
    """
    self._add_processor(MerchantColossusClickSliceFeatureV2Enricher(kwargs))
    return self

  def merchant_goods_gsu_dp_topn_sort(self, **kwargs):
    """
    MerchantGoodsGsuDpTopnSortEnricher
    ------
    
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个的colossus_key

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_key_attr` : [string] 取自colossus的key列表

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `field1_attr` : [string] 第一个side info的attr name，从field1到field20

    `category_attr` : [string] cate对应的cate attr name，会移位解析出1、2、3级类目

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    示例
    ------
    ``` python
    .merchant_goods_gsu_dp_topn_sort(
      common_distance_ptr_attr="fr_distance",
      colossus_key_attr="colossus_item_id",
      top_n=10,
      min_distance=0.01,
      output_item_colossus_distance_attr="fr_distance_item_dist"
    )
    ``` 
    """
    self._add_processor(MerchantGoodsGsuDpTopnSortEnricher(kwargs))
    return self

  def merchant_goods_gsu_tower_sort_post(self, **kwargs):
    """
    MerchantGoodsGsuTowerSortPostEnricher
    ------
    
    将 common_attr中的 `*std::vector<double>` 转化为各个item对应的 `vector<double>`，排序
    并返回最大的n个，和对应的 output_sign 和 output_slots，能自主控制输入的side info个数和输出的
    slot和sign的个数

    参数
    ------
    `common_distance_ptr_attr` : [string] 待转换的 common attr

    `colossus_key_attr` : [string] 取自colossus的key列表

    `timestamp_attr` : [string] pid对应的timestamp attr name

    `field1_attr` : [string] 第一个side info的attr name，从field1到field20

    `category_attr` : [string] cate对应的cate attr name，会移位解析出1、2、3级类目

    `output_sign_attr` : [string] pid对应的output sign attr name

    `output_slot_attr` : [string] pid对应的output slot attr name

    `top_n` : [int] 返回每个item对应的最大n个distance值和对应的pid, 为空时返回所有

    示例
    ------
    ``` python
    .merchant_goods_gsu_tower_sort_post(
      common_distance_ptr_attr="fr_distance",
      colossus_key_attr="colossus_item_id",
      timestamp_attr="item_click_timestamp",
      field1_attr="item_item_id",
      field2_attr="item_seller_id",
      field3_attr="item_click_timestamp",
      field4_attr="item_llsid",
      field5_attr="item_real_price",
      field6_attr="item_origin_price",
      field7_attr="item_click_from",
      field8_attr="item_click_flow_type",
      field9_attr="item_label",
      field10_attr="item_show_timestamp",
      field11_attr="item_detail_page_view_time",
      field12_attr="item_leaf_category",
      field13_attr="item_real_seller_id",
      field14_attr="item_good_type",
      category_attr="item_category",
      slot_as_attr_name=True,
      output_sign_attr="item_gsu_signs_extend",
      output_slot_attr="item_gsu_slots_extend",
      slots_id=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
      mio_slots_id=[11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30],
      top_n=10,
      min_distance=0.01,
      output_item_colossus_distance_attr="fr_distance_item_dist"
    )
    ``` 
    """
    self._add_processor(MerchantGoodsGsuTowerSortPostEnricher(kwargs))
    return self

  def merchant_gsu_common_colossus_resp_retriever(self, **kwargs):
    """
    MerchantCommonColossusRespRetriever
    ------

    ------
    `colossus_resp_attr`: [string] outupt from the colossus processor

    `colossus_service_name`: [string]

    `item_key_field`: [string]

    `item_time_field`: [string]

    `item_fields`: [dict]

    `filter_future_items` : [boolean] 是否只取时间在 request time 之前的 item 数据, 默认为 false

    `to_common_attr` : [boolean] 是否把数据解析后填充到 common attr, 默认为 false

    `is_combine_item_key_and_time` : [boolean] 默认为 false

    示例
    ------
    ``` python
    .merchant_gsu_common_colossus_resp_retriever(colossus_resp_attr="colossus_output",
                            colossus_service_name="grpc_colossusSimV2",
                            item_key_field="photo_id",
                            item_time_field="timestamp",
                            item_fields=dict(photo_id="photo_id",
                                             author_id="author_id",
                                             duration="duration",
                                             play_time="play_time",
                                             tag="tag",
                                             channel="channel",
                                             label="label",
                                             timestamp="timestamp",
                                             user_latitude="user_latitude",
                                             user_longitude="user_longitude",
                                             photo_latitude="photo_latitude",
                                             photo_longitude="photo_longitude"))
    ```
    """
    self._add_processor(MerchantCommonColossusRespRetriever(kwargs))
    return self

  def merchant_colossus_dedup_enricher(self, **kwargs):
    """
    MerchantColossusDedupEnricher
    ------

    ------
    `colossus_raw_itemid_attr`: [string]
    `colossus_itemkey_attr`: [string]
    `output_deduped_colossus_itemkey_attr`: [string]
    `timestamp_attr`: [string]
    `playtime_attr`: [string]

    示例
    ------
    ``` python
    .merchant_colossus_dedup_enricher(
      colossus_raw_itemid_attr="colossus_pid",
      colossus_itemkey_attr="colossus_itemkey",
      output_deduped_colossus_itemkey_attr="colossus_itemkey_dedup",
      timestamp_attr="timestamp",
      playtime_attr="playtime"
    )
    ```
    """
    self._add_processor(MerchantColossusDedupEnricher(kwargs))
    return self

  def item_predict_by_kml_grpc_enrich(self, **kwargs):
      """
      ItemPredictByKmlEnricher
      ------
      从远程 red point slide model grpc 服务获取 pctr。

      参数配置
      ------
      `timeout_ms`: [int] 必配项，调远程排序 grpc 服务超时时间，建设值 50ms

      `kess_service_name`: [string] 必配项，调远程排序 grpc 服务 kess 名

      `common_attrs_oredr_kconf`: [list] 必配项，string, 本 processor 需要输入的 common 特征 kconf,kconf 类型 list_string

      `item_attrs_oredr_kconf`: [list] 必配项，string, 本 processor 输出的 item 特征 kconf,kconf 类型 list_string

      `model_name`: [string] 选配项，调远程排序 grpc 服务的模型名称，默认值 1pp-gmv-lgb

      `kess_caller_name`: [string] 选配项，调远程排序 grpc 服务的 kess_caller_name，默认值 InferKessClient

      调用示例
      ------
      ``` python
      .item_predict_by_kml_grpc_enrich(
        timeout_ms=50,
        
        kess_service_name="grpc_hot_live_gmv_tree",

        common_attrs_oredr_kconf="platecoDev.merchantReco.gbdt_common_attrs",

        item_attrs_oredr_kconf="platecoDev.merchantReco.gbdt_items_attrs",

        output_item_attrs=[
            "gbdt_pred",
        ]
      )
      ```
      """
      self._add_processor(ItemPredictByKmlEnricher(kwargs))
      return self

  def merchant_perflog_item_attr_value(self, **kwargs):
    """
    MerchantItemAttrValuePerflogObserver
    ------
    支持 分 item_attr 打点上报：
    参数配置
    ------
    `check_point`: [string] [动态参数] 自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `input_white_list_common_attr`: [String] [动态参数] 分组itemAttr 白名单，如aId白名单

    `input_item_attrs`: [list] 需要统计的 item_attr 名称列表

    `groupby_item_attr`: [String] 需要分组的 item_attr 名称列表

    `local`: [bool] 选配项，统计结果直接写到本地 perflog.log 文件而不是上传到 clickhouse，默认为 False。

    `perf_base`: [int] 打点时的放大倍数，用于保留小数点，默认为 1000000L，注意修改这个值会导致 Grafana 上打点显示异常，使用时需要注意。

    调用示例
    ------
    ``` python
    # 统计 default 流程中排序阶段的各个 pxtr 均值
    .merchant_perflog_item_attr_value(
      check_point="default.ranking",
      input_item_attrs=["pctr", "pltr", "pftr"],
      groupby_item_attr= "aId",
      input_white_list_common_attr="perf_white_list"
    )
    ```
    """
    self._add_processor(MerchantItemAttrValuePerflogObserver(kwargs))
    return self

  def merchant_live_gsu_post_gather_enricher(self, **kwargs):
    """
    MerchantLiveGsuPostGatherEnricher
    """
    self._add_processor(MerchantLiveGsuPostGatherEnricher(kwargs))
    return self

  def live_gsu_aid2aid_with_slot(self, **kwargs):
    """
    MerchantLiveGsuAid2aidWithSlotEnricher
    ------
    """
    self._add_processor(MerchantLiveGsuAid2aidWithSlotEnricher(kwargs))
    return self

  def merchant_retrieve_from_sample_join_api_request(self, **kwargs):
    """
    MerchantSampleJoinApiRequestRetriever
    ------
    """
    self._add_processor(MerchantSampleJoinApiRequestRetriever(kwargs))
    return self
  def merchant_delegate_enricher(self, **kwargs):
    """
    MerchantDelegateEnricher
    ------
    """
    self._add_processor(MerchantDelegateEnricher(kwargs))
    return self

  def merchant_gsu_good_click_session(self, **kwargs):
    """
    MerchantGsuGoodClickSessionEnricher
    ------
    电商商品点击session特征
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr

    `limit_num`: [int] 截断数量

    `n_minute_ago`: [int] 前移时间

    `session_time_range`: [string] 序列特征窗口

    `suffix_str`: [string]后缀,默认为空字符串
    调用示例
    ------
    ``` python
    .merchant_gsu_good_click_session(
      colossus_resp_attr = "colossus_resp_click_good_item",
      limit_num = 100,
      n_minute_ago = 0,
      session_time_range = "60;180;360;720"
    )
    ```
    """
    self._add_processor(MerchantGsuGoodClickSessionEnricher(kwargs))
    return self

  def merchant_video_play_session(self, **kwargs):
    """
    MerchantVideoPlaySessionEnricher
    ------
    电商短视频播放序列session特征
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr

    `if_parse_to_pb_attr`: [bool] 接收的colossus_resp是否是pb格式

    `keep_cart`: [bool] 是否筛选出挂车视频

    `keep_p2l`: [bool] 是否筛选出live头像视频

    `limit_num`: [int] 截断数量

    `n_minute_ago`: [int] 前移时间

    `session_time_range`: [string] 序列特征窗口

    `suffix_str`: [string]后缀,默认为空字符串
    调用示例
    ------
    ``` python
    .merchant_video_play_session(
      colossus_resp_attr = "colossus_resp_merchant_video_item",
      if_parse_to_pb_attr = False,
      keep_cart = True,
      keep_p2l = False,
      limit_num = 100,
      n_minute_ago = 0,
      session_time_range = "60;180;360;720"
    )
    ```
    """
    self._add_processor(MerchantVideoPlaySessionEnricher(kwargs))
    return self

  def merchant_gsu_good_order_session(self, **kwargs):
    """
    MerchantGsuGoodOrderSessionEnricher
    ------
    电商商品点击session特征
    ------
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr

    `limit_num`: [int] 截断数量

    `n_minute_ago`: [int] 前移时间

    `session_time_range`: [string] 序列特征窗口

    `suffix_str`: [string]后缀,默认为空字符串
    调用示例
    ------
    ``` python
    .merchant_gsu_good_order_session(
      colossus_resp_attr = "colossus_resp_order_good_item",
      limit_num = 100,
      n_minute_ago = 0,
      session_time_range = "60;180;360;720"
    )
    ```
    """
    self._add_processor(MerchantGsuGoodOrderSessionEnricher(kwargs))
    return self
  
  def merchant_good_recent_session(self, **kwargs):
    """
    MerchantGoodRecentSessionEnricher
    ------
    电商商品点击近期session特征
    ------
    `colossus_type`: [string] 只支持goodclick和goodorder
    `colossus_resp_attr`: [string] colossus_resp 输出的 attr
    `if_parse_to_pb_attr`: [bool] 
    `session_num_range`: [string] 序列个数
    
    `aid`: [string]
    `c1id`: [string]
    调用示例
    ------
    ``` python
    .merchant_good_recent_session(
      colossus_type='goodclick',
      colossus_resp_attr='colossus_resp_click',
      if_parse_to_pb_attr=True,
      session_time_range = "1;3;5;10",
      aid='aId',
      c1id='c1id'
    )
    ```
    """
    self._add_processor(MerchantGoodRecentSessionEnricher(kwargs))
    return self

  def merchant_ltv_feat_enricher(self, **kwargs):
    """
    MerchantLtvFeatEnricher
    ------
    抽取ltv rodis作为label
    """
    self._add_processor(MerchantLtvFeatEnricher(kwargs))
    return self

  def merchant_ltv_feat_ext_enricher(self, **kwargs):
    """
    MerchantLtvFeatExtEnricher
    ------
    抽取ltv 交互rodis作为label
    """
    self._add_processor(MerchantLtvFeatExtEnricher(kwargs))
    return self

  def merchant_gsu_a2a_ext_enricher(self, **kwargs):
    """
    MerchantGsuA2aExtEnricher
    ------
    a2a检索直播 colossus v4
    ------
    """
    self._add_processor(MerchantGsuA2aExtEnricher(kwargs))
    return self 

  def merchant_merge_tables(self, **kwargs):
    """
    MerchantMergeTablesMixer
    ------
    电商merchant 对不同table进行合并

    参数
    ------
    `ouput_table`: [string] 必填，最终合并的table名称

    `input_tables`: [dict] 必填，需要合并的源数据表
      - table_name: 源数据表name
      - item_attrs: [list] 该表合并到目的表时 要传递的 item-attr 集合
    
    `item_overwrite`: [bool] 是否进行强覆盖，默认为false. 该开关用于控制算子内所有tables，如果需要分开控制，将table拆开，多次调用

    示例
    ------
    ``` python
    .merchant_merge_tables(
      output_table = "target_table",
      input_tables = [
        {
          "table_name": "source1_table",
          "attrs": [
            "attr1", "attr2", "attr3", ...
          ],
        },
        {
          "table_name": "source2_table",
          "attrs": [
            "attr4", "attr5", "attr6", ...
          ],
        },
      ],
      item_overwrite: false,  //相同attr是否覆盖，默认是false
    )
    ```
    """
    self._add_processor(MerchantMergeTablesMixer(kwargs))
    return self

  def merchant_leaf_shared_copy_retrieve(self, **kwargs):
    """
    MerchantLeafSharedCopyRetriever
    ------
    单列 slide_leaf 对当前链路对其它链路结果的复制召回

    参数配置
    ------

    调用示例
    ------
    ``` python
      .merchant_leaf_shared_copy_retrieve(
        shared_list = [
          {
            "target_attr": "is_living_copied_photo",
            "target_value": 1,
            "from_table": "consume",
          },
        ],
      )
    ```
    """
    self._add_processor(MerchantLeafSharedCopyRetriever(kwargs))
    return self
  
  def merchant_ltv_seq2seq_enricher(self, **kwargs):
    """
    MerchantLtvSeq2seqEnricher
    ------
    """
    self._add_processor(MerchantLtvSeq2seqEnricher(kwargs))
    return self
 
  def merchant_coupon_feature(self, **kwargs):
    """
    MerchantCouponFeatureEnricher
    ------
    """
    self._add_processor(MerchantCouponFeatureEnricher(kwargs))
    return self

  def merchant_retrieve_by_memory_data(self, **kwargs):
    """
    MerchantLeafMemoryDataRetriever
    ------
    从 memory_data 中读取数据并召回 item

    参数配置
    ------
    `data_key`: [string] [动态参数] 选配项, memory_data 数据的 key, 与 data_ptr_attr 二选一配置

    `data_ptr_attr`: [string] 选配项, memory_data 数据的 内存地址，若数据多次被使用，建议先抽取 ptr, 再使用

    `data_type`: [string] 必填项, memory_data 的数据类型，仅支持 uint64_vector, uint64_set, int64_int64_map, 
      uint64_uint64_map, uint64_double_map, string_uint64_vector_map, string_uint64_set_map,
      uint64_uint64_set_map。 如有其他需要，自行添加 

    `retrieve_num`: [int] [动态参数] 召回个数

    `reason`: [int] 召回原因

    `item_type`: [int] 选配项, 召回 item 的 item_type, 和 item_id 一起产生 item_key, 默认值为 0

    `random_pick`: [bool] 选配项，当数据集超过 retrieve_num 时，将随机抽取 retrieve_num 个 item, 否则按顺序抽取，默认值为 False

    `enable_browse_set_filter`: [bool] 选配项，召回的 item 都经过 browse_set 过滤, 默认值为 False

    `value_from`: [string] 选配项, 项数据类型为 map 类型是， 选择 "key" or "value" 做为召回源。默认为 "key"。

    `retrive_all_second_level_item`: [bool] 选配项，数据类型为 string_uint64_vector_map, string_uint64_set_map,
      uint64_uint64_set_map 时，是否召回所有 value 集合。默认 False

    `selected_map_key_attr`: [string] 选配项，数据类型为 string_uint64_vector_map, string_uint64_set_map,
      uint64_uint64_set_map 时，召回某些 key 对应的 value 集合。 此项配置时， `retrive_all_second_level_item` 将失效,
      data_type 的 key 为 string 时， 填入 STRING/STIRNG_LIST 类型的 attr_name,
      data_type 的 key 为 int 时， 填入 INT/INT_LIST 类型的 attr_name 

    调用示例
    ------
    ``` python
    .merchant_retrieve_by_memory_data(
      data_key = "coldstart_white_list",
      data_type = "string_uint64_vector_map",
      reason = 3000,
      item_type = 0
      random_pick = True,
      enable_browse_set_filter = True,
      selected_map_key_attr = "s_merchant_coldstart_hot_pids_strategy_name",
    )
    ```
    """
    self._add_processor(MerchantLeafMemoryDataRetriever(kwargs))
    return self
  
  def merchant_coupon_gsu_enricher(self, **kwargs):
    """
    MerchantCouponGsuEnricher
    ------
    """
    self._add_processor(MerchantCouponGsuEnricher(kwargs))
    return self

  def infer_enrich(self, **kwargs):
    """
    RecoInferEnricher
    ------
    调用另一个基于 CommonLeaf 协议的远程 Infer 服务进行计算，并填充返回的属性，参考 DelegateEnrich。

    参数配置
    ------
    `model_family_kconf` [string] 必填项，model family 的配置 kconf 。

    `model_family_name` [string] 必填项，model family 的名字，上一步的 `model_family_kconf` 会包含多个模型名字，这里只能配置其中一个，代表使用哪个模型，必须是 kconf 中存在的模型。

    `shard_num`: [int] 被调服务的 shard 数，该 processor 会并发请求多个 shard，结果合并，默认为 1。

    `shard_id_offset`: [int] 被调服务的 shard_id 起始偏移量，默认为 0，即 shard 号从 s0 开始

    `shard_by_item_attr`: [string] 按给定的 item attr 将 item list 拆分成不同的 shard 去请求下游服务，默认为空，此时对于各个 shard 的请求完全相同。

    `consistent_hash`: [bool] 是否按 user_id 或 device_id 对请求进行一致性 hash 的分发，以保证同一用户的请求始终落在同一索引机器上，默认 False

    `timeout_ms`: [int] [动态参数] 选填项，gRPC 超时时间，默认为 300ms。

    `request_type`: [string] [动态参数] 选填项，请求的 request type，默认为本 leaf 当前的 request type。

    `use_item_id_in_attr`: [string] 选填项，若设置则使用指定的 ItemAttr 下的 int 值替代实际 Item 的 item_key，填充进 request 用于发送给下游（注意是替换 item_key，不是 item_id，配置名中的 use_item_id 字眼有歧义）

    `send_item_attrs`: [list] 选填项，发送的 item attr 列表，默认不发送 item attr，支持对 attr 重命名发送。

    `send_item_attrs_in_name_list`: [string] 选填项，从指定的 string_list common attr 中获取需要发送的 item attr 的 name list

    `send_common_attrs`: [list] 选填项，发送的 common attr 列表，默认不发送 common attr，支持对 attr 重命名发送。

    `send_common_attrs_in_request`: [bool] 选填项, 是否将上游发送过来的 request 中的全部 common_attr 发送给下游, 默认为 false。

    `exclude_common_attrs`: [list] 选填项, 发送给下游时，需要过滤的 common_attr. 一般配合 send_common_attrs_in_request 使用

    `recv_item_attrs`: [list] 选填项，接收的 item attr 列表，默认不接收 item attr，支持对 attr 重命名保存。

    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    `use_packed_item_attr`: [bool] [动态参数] 选填项，是否要求服务端用 packed_item_attr 格式返回 item_attr 以提高数据读写性能，缺省时会优先尝试使用 packed_item_attr 格式，若获取数据失败会取消 packed_item_attr 格式的使用

    `infer_output_type`: [int] 选填项，要求 tower infer server 用指定的 output_type（与服务端 [fetch_tower_remote_pxtr](https://dragonfly.corp.kuaishou.com/#/api/embed_calc?id=fetch_tower_remote_pxtr) 的 output_type 配置功能相同）返回 pxtr 数据，默认为 -1

    `use_sample_list_attr_flag`: [bool] 选填项，是否使用 sample_list 服务获取的 common attrs，默认为 false

    `sample_list_common_attr_key`: [string] 选填项，从指定的 string_list common attr 中获取 sample_list attr 所在的 attr name 列表

    `flatten_sample_list_attr`: [bool] 选填项，是否使用压缩格式发送 sample_list 服务获取的 common attrs，默认为 false

    `flatten_sample_list_attr_to`: [string] 选填项，将 flatten 后的 sample_list attr 以指定的 attr name 发送，仅当 flatten_sample_list_attr=true 时有效，默认为 "kuiba_user_attrs"

    `ttl_seconds`: [int] 选配项，在创建 request 的时候，底层默认会复用 protobuf 的对象空间，如果发生像 UserInfo 一样长期复用导致内存无限增长的情况，可通过该项配置来定期清理内存空间，默认值为 3600


    调用示例
    ------
    ``` python
    .infer_enrich(
      model_family_kconf="reco.infra.merchantLiveModels",
      model_family_name="showc_predict_service_name_add_label",
      send_item_attrs = ["pId", "aId"],
      send_common_attrs = ["uId"],
      recv_item_attrs = ["ctr"],
      request_type = "default",
      use_packed_item_attr = True,
    )
    ```
    """
    self._add_processor(RecoInferEnricher(kwargs))
    return self
  
  """
  InferScoreToKafkaObserver
  ------
  必须跟 `infer_enrich` 一起使用，把 `infer_enrich` 的原始结果发送到 kafka。

  参数配置
  ------
  `model_family_name`: [string] 必填项, famly name

  `topic_name`: [string] 必填项， kafka topic name

  调用示例
  ------
  ``` python
  .infer_score_to_kafka(
    name = "infer_score_to_kafka" + self.name_suffix,
    model_family_name = 'showc_predict_service_name_add_label',
    topic_name='bigriver_live_test',
  )
  ```
  """
  def infer_score_to_kafka(self, **kwargs):
    self._add_processor(InferScoreToKafkaObserver(kwargs))
    return self

  def get_common_attrs_from_request(self, **kwargs):
    """
    GetCommonAttrsFromRequestEnricher
    ------
    从 context 中读取上游发送的所有common attr并返回到一个stringList

    参数配置
    ------
    `save_attr_names_to_attr`: [string] 用于存储所有上游发送的common attr的string

    """
    self._add_processor(GetCommonAttrsFromRequestEnricher(kwargs))
    return self

  def get_common_attrs_from_request(self, **kwargs):
    """
    GetCommonAttrsFromRequestEnricher
    ------
    从 context 中读取上游发送的所有common attr并返回到一个stringList

    参数配置
    ------
    `save_attr_names_to_attr`: [string] 用于存储所有上游发送的common attr的string

    """
    self._add_processor(GetCommonAttrsFromRequestEnricher(kwargs))
    return self
  

  """
  Ranking
  """
  def merchant_consistent_ranking_ranker(self, **kwargs):
    """
    MerchantConsistentRankingEnricher
    ------
    对item结果集,进行一致性排序

    参数配置
    ------
    #### 如下均支持动参
    `revenue_params`: [list] 营收xtr name list
    `revenue_weights`: [list] 营收xtr 权重 list
    `content_params`: [list] 内容xtr name list
    `content_weights`: [list] 内容xtr 权重 list
    `content_coeff`: [list] 内容xtr 次方权重 list
    `content_bias`: [list] 内容xtr 基础bias list
    `c_bonus`: [string] bonus name
    `b_bonus`: [string] bonus name
    `bonus_weight`: [double] bonus计算公式权重
    `merchant_score_weight`: [double] 电商流量价值计算 a1
    `merchant_score_bias`: [double] 电商流量价值计算 b1
    `merchant_score_exp`: [double] 电商流量价值计算 c1
    `ue_score_weight`: [double] 内容流量价值计算 a1
    `ue_score_bias`: [double] 内容流量价值计算 b1
    `ue_score_exp`: [double] 内容流量价值计算 c1
    `ue_score_lower_bound`: [double] 内容流量价值分 下界
    `ue_score_upper_bound`: [double] 内容流量价值分 上界
    ------
    `export_real_revenue_name`: [string] export 真实营收 默认值real_revenue_score
    `export_ue_score_name`: [string] export 内容体验 默认值ue_score
    `export_total_bonus_name`: [string] export bonus总值 默认值total_bonus
    `export_merchant_score_name`: [string] export 电商营收总分 默认值merchant_score
    `export_traffic_worth_score_name`: [string] export 流量价值总分 默认值traffic_worth_score
    `export_ue_score_final_name`: [string] export 内容体验(abc变换后) 默认值ue_score_final
    `export_merchant_score_final_name`: [string] export 电商营收总分(abc变换后) 默认值merchant_score_final
    """
    self._add_processor(MerchantConsistentRankingEnricher(kwargs))
    return self

  def merchant_consistent_ranking_ranger(self, **kwargs):
    """
    MerchantConsistentRankingArranger
    ------
    对item结果集,进行一致性控量

    参数配置
    ------
    `request_type`: [string] 请求type name
    `dyeing_reason`: [string] 染色resason name
    `sorter_name`: [string] 结果集排序attr_name,默认traffic_worth_score
    `desc`: [bool] 默认true
    `limit_size`: [string] 截断attr name,不配置默认截断6
    """
    self._add_processor(MerchantConsistentRankingArranger(kwargs))
    return self

  def merchant_live_ue_score_enricher(self, **kwargs):
    """
    MerchantLiveUeScoreEnricher
    ------
    电商直播计算 ue_score

    参数配置
    ------
    #### 如下均支持动参
    `content_params`: [list] 内容xtr name list
    `content_weights`: [list] 内容xtr 权重 list
    `content_coeff`: [list] 内容xtr 次方权重 list
    `content_bias`: [list] 内容xtr 基础bias list
    `ue_score_lower_bound`: [double] 内容流量价值分 下界
    `ue_score_upper_bound`: [double] 内容流量价值分 上界
    ------
    `export_ue_score_name`: [string] export 内容体验 默认值ue_score
    """
    self._add_processor(MerchantLiveUeScoreEnricher(kwargs))
    return self
  
  def merchant_channel_sort(self, **kwargs):
    """
    MerchantChannelSortArranger
    多通道排序
    参数配置
    ------
      - `channel_queue_names`: [string list][动态参数] 实际队列的名字
    
      - `input_count_threshold`: [int][动态参数] 进行多通道排序最小输入 Item 数量
    
      - `output_count`: [int][动态参数] 进行多通道排序输出 Item 数量
      
      - `queue_weight_attrs`: [string list] 表示各队列的权重的 Common Attr 名字
      
      - `queue_score_attrs`: [string list] 表示各队列的分数的 Item Attr 名字
      
      - `queue_flag_attrs`: [string list] 标志是否属于某队列的 Item Attr 名字
      
      - `enable_dryrun_mode`: [bool] 标志是否进行粗排精排重复率的衡量
      
      - `dryrun_mode_result_attr`: [string] dryrun 模式结果保存的 Item Attr 名字
    ------
    ``` python
    .merchant_channel_sort(
      channel_queue_names="{{mc_channel_queue_names}}"
      input_count_threshold=200,
      output_count=200,
      queue_weight_attrs=["mc_csqw_follow, "mc_csqw_hot"],
      queue_score_attrs=["mc_csqs_follow", "mc_csqs_hot"],
      queue_flag_attrs=["mc_csqf_follow", "mc_csqf_hot"],
      enable_dryrun_mode=False,
      dryrun_mode_result_attr = "merchant_dryrun_mode_result"
    )
    ```
    """
    self._add_processor(MerchantChannelSortArranger(kwargs))
    return self
  
  def merchant_perflog_item_attr(self, **kwargs):
    """
    MerchantPerflogItemAttrObserver
    -------
    参数
    ------
    `mode`: [string] 设置 perf 上报的类型，支持 count/interval。

    `value`: [int/double] item_attr, 选配项 设置 perf 上报的 count，mode 为 count 时 value 值为 1，mode 为 interval 时去取 item_attr 的值作为 value。
    
    `namespace`: [string] [动态参数] 设置 perf 上报的 namespace。
    
    `subtag`: [string] [动态参数] 设置 perf 上报的 sub_tag。
    
    `extra1`: [string] [动态参数] 选配项 设置 perf 上报的 ext1，默认为当前服务名。
    
    `extra2`: [string] [动态参数] 选配项 设置 perf 上报的 ext2，默认为当前请求的 request_type 值。
    
    `extra3`: [string/int] item_attr, 选配项 设置 perf 上报的 ext3，默认为空字符串。int的取值范围最好不要太大，否则开销会很大。
    
    `extra4`: [string/int] item_attr, 选配项 设置 perf 上报的 ext4，默认为空字符串。int的取值范围最好不要太大，否则开销会很大。
    
    `extra5`: [string] [动态参数], common_attr, 选配项 设置 perf 上报的 ext5，默认为空字符串。
    
    `extra6`: [string] [动态参数], common_attr, 选配项 设置 perf 上报的 ext6，默认为空字符串。

    调用示例
    ------
    ``` python
    .merchant_perflog_item_attr(
      mode='interval', 
      namespace='common.leaf',
      subtag='mc_queue_show',
      value='mc_channel_origin_idx', 
      extra3='mc_channel_origin_name',
      extra4='_REASON_',
    )
    ```
    """
    self._add_processor(MerchantPerflogItemAttrObserver(kwargs))
    return self

  def merchant_goods_slice_feature_enricher(self, **kwargs):
    """
    MerchantGoodsSliceFeatureEnricher
    -------
    参数
    ------
    `input_int_attr`: [必填][string] 批量的 int 数据输入源。
    `input_keys_attr`: [必填][string] 批量的 int 数据输入源对应的pid。
    调用示例
    ------
    ``` python
    .merchant_goods_slice_feature_enricher(
      input_int_attr='int_batch_output', 
      input_keys_attr='item_keys',
    )
    ```
    """
    self._add_processor(MerchantGoodsSliceFeatureEnricher(kwargs))
    return self

  def item_list_to_string(self, **kwargs):
    """
    MerchantItemListToStringEnricher
    ------
    将 list item attr 转化为 string

    参数配置
    ------
    `attr_seperator`: [string] attr 之间的分隔符，默认为 ','

    `input_item_attr`: [string] 带转化的 attr 字段，仅支持 list item attr

    `output_item_attr`: [string] 输出的 item attr
    
    `topk`: [int] 截断topk

    调用示例
    ------
    ``` python
    .item_list_to_string(
      input_item_attr="ctr_list",
      output_item_attr="concat_str"
    )
    ```
    """
    self._add_processor(MerchantItemListToStringEnricher(kwargs))
    return self
  
  def merchant_transform_list_attr(self, **kwargs):
    """
    MerchantTransformListAttrEnricher
    -----
    对 common list attr 进行排序、过滤后生成新的 common list attr

    参数
    -----
    `configs`: [list] transform 配置
      - `is_dst_common`: [bool] 输出是否是 common attr, 默认为 true

      - `sort_attr`: [string] 用于排序的 attr

      - `is_sort_attr_common`: [bool] 用于排序的 attr 是否是 common attr, 默认为 true

      - `sort_desc`: [bool] 是否降序排序，默认为 true

      - `limit_size`: [int] 新 list attr 最大长度, 默认为 -1 表示没有限制

      - `output_sort_indices_attr`: [string] 输出排序后的 indices attr, 默认为 None

      - `output_mask_flags_attr`: [string] 输出 mask flags attr, 默认为 None

      - `use_double_mask`: [bool] 是否使用 double mask, 默认为 false

      - `filter_configs`: [list] filter 配置
        - `attr`: [string] 用于 filter 的 attr
        - `is_attr_common`: [bool] 用于 filter 的 attr 是否是 common attr, 默认为 true
        - `lower_bound_attr`: [string] 用于 filter 的 attr 的 lower bound, 默认为 None
        - `upper_bound_attr`: [string] 用于 filter 的 attr 的 upper bound, 默认为 None
        - `not_equal_to_attr`: [string] 不等于 attr, 默认为 None
        - `is_not_equal_to_attr_common`: [bool] 不等于 attr 是否是 common attr, 默认为 true

      - `attr_configs`: [list] attr 配置
       - `src_attr`: [string] 输入 attr name
       - `dst_attr`: [string] 输出 attr name
       - `is_src_attr_common`: [bool] 输入 attr 是否是 common attr, 默认为 true

    调用示例
    -----
    .merchant_transform_list_attr(
      configs=[
        dict(
          is_dst_common=True
          sort_attr="timestamp",
          sort_desc=True,
          filter_configs=[
            dict(attr="watch_time", min_val=60),
          ]
          limit_size=50,
          attr_configs=[
            dict(src_attr="author_id", dst_attr="new_author_id"),
            dict(src_attr="watch_time", dst_attr="new_watch_time"),
          ]
        ),
      ]
    )
    """
    self._add_processor(MerchantTransformListAttrEnricher(kwargs))
    return self
  
  def merchant_group_list_attr(self, **kwargs):
    """
    MerchantGroupListAttrEnricher
    -----
    将 list attr 按照 key group, 并应用 min, max, sum, avg 操作

    参数
    -----
    `configs`: [list] 配置
      - `is_common`: [bool] 是否是 common attr, 默认为 true
      - `key_attr`: [string] 用户 group 的 key
      - `out_key_attr`: [string] 输出 key attr
      - `out_count_attr`: [string] 输出 count attr
      - `agg_configs`: [list] 聚合设置
        - `src_attr`: [string] 输入 attr name
        - `op`: [string] 可选择 (min, max, sum, avg)
        - `dst_attr`: [string] 输出 attr name
        - `dst_type`: [string] 输出 attr type, 可选择 (int_list, float_list) 默认和 src_attr 保持一致

    调用示例
    -----
    ```python
    .group_list_attr(
      configs=[
        dict(
          key_attr="author_id",
          out_key_attr="unique_author_id",
          out_count_attr="author_count",
          agg_configs=[
            dict(
              src_attr="gift_amt",
              op="sum",
              dst_attr="gift_amt_sum",
              dst_type="int_list",
            ),
          ],
        ),
      ],
    )
    ```
    """
    self._add_processor(MerchantGroupListAttrEnricher(kwargs))
    return self
  
  def bid_formula_enricher(self, **kwargs):
    """
    BidFormulaEnricher
    -------
    参数配置
    ------
    `common_strategy_id_list_attr_name`: [必填][string] 从common attr获取策略的id列表的字段名

    `common_strategy_param_length_list_attr_name`: [必填][string] 从common attr获取策略的name列表的字段名

    `common_strategy_param_name_list_attr_name`: [必填][string] 从common attr获取策略的参数列表的字段名

    `common_strategy_formula_list_attr_name`: [必填][string] 从common attr获取策略的表达式列表的字段名,表达式例"1100.0*ctr*5.4+20"

    `common_strategy_name_list_attr_name`: [必填][string] 从common attr获取策略的名称列表的字段名

    `item_strategy_depend_scores`: [必填][list] 通用参数的列表，支持重命名

    `item_strategy_id_list_attr_name`: [必填][string] 从给定item attr获取所属策略的id的字段名

    `item_strategy_params_value_list_attr_name`: [必填][string] 从给定item attr获取策略的参数值的字段名

    `item_strategy_bonus_list_attr_name`: [必填][string] 各策略计算得到bonus结果list

    `bonus_attrs`: [必填][string list] 计算的结果的字段名列表

    `stage`: [必填][string] 当前stage名称,和SubStage枚举保持一致

    `biz_attr_name`: [必填][string] 获取biz的common attr字段名

    调用示例
    ------
    ``` python
    .bid_formula_enricher(
      common_strategy_id_list_attr_name='common_strategy_id_list',
      common_strategy_param_length_list_attr_name='common_strategy_param_length_list',
      common_strategy_param_name_list_attr_name='common_strategy_param_name_list',
      common_strategy_formula_list_attr_name='common_strategy_formula_list',
      common_strategy_name_list_attr_name='common_strategy_name_list',
      item_strategy_depend_scores=[{"name": "live_goods_cart_cvr", "as": "cvr"},{"name": "live_goods_cart_ctr", "as": "ctr"}],
      item_strategy_id_list_attr_name='item_strategy_id_list',
      item_strategy_params_value_list_attr_name='item_strategy_params_value_list',
      item_strategy_bonus_list_attr_name='item_strategy_bonus_list',
      bonus_attrs=["test","test2"],
      stage='LEAF_RETRIEVE',
      biz_attr_name="biz"
    )
    ```
    """
    self._add_processor(BidFormulaEnricher(kwargs))
    return self

  def merchant_live_order_common_feature_extract_enricher(self, **kwargs):
    """
    MerchantLiveOrderCommonFeatureExtractEnricher
    -------
    参数
    ------
    `order_item_colossus_resp_attr`: [必填][string] order_item_colossus_resp_attr。
    `leaf_request_ms_attr`: [必填][string] leaf_request_ms_attr。
    `filter_time`: [必填][int] filter_time。
    调用示例
    ------
    ``` python
    .merchant_live_order_common_feature_extract_enricher(
        order_item_colossus_resp_attr="order_colossus_output_lyc",
        leaf_request_ms_attr="request_time_spucom",
        filter_time=0,
    )
    ```
    """
    self._add_processor(MerchantLiveOrderCommonFeatureExtractEnricher(kwargs))
    return self

  def merchant_live_order_noncommon_feature_extract_enricher(self, **kwargs):
    """
    MerchantLiveOrderNoncommonFeatureExtractEnricher
    -------
    参数
    ------
    `order_item_colossus_resp_attr`: [必填][string] order_item_colossus_resp_attr。
    `leaf_request_ms_attr`: [必填][string] leaf_request_ms_attr。
    `filter_time`: [必填][int] filter_time。
    `aid_list_attr`: [必填][string] aid_list_attr。
    调用示例
    ------
    ``` python
    .merchant_live_order_noncommon_feature_extract_enricher(
        order_item_colossus_resp_attr="order_colossus_output_lyc",
        leaf_request_ms_attr="request_time_spunon",
        filter_time=0,
        aid_list_attr="order_aId_list",
    )
    ```
    """
    self._add_processor(MerchantLiveOrderNoncommonFeatureExtractEnricher(kwargs))
    return self


  def merchant_live_scart_price_extract_enricher(self, **kwargs):
    """
    MerchantLiveScartPriceExtractEnricher
    -------
    参数
    ------
    `sCartItemQuotedPriceList`: [必填][string] sCartItemQuotedPriceList。
    `sCartItemList`: [必填][string] sCartItemList。
    `sCartItemCate1IdList`: [必填][string] sCartItemCate1IdList。
    `sCartItemCate2IdList`: [必填][string] sCartItemCate2IdList。
    `sCartItemCate3IdList`: [必填][string] sCartItemCate3IdList。
    调用示例
    ------
    ``` python
    .merchant_live_scart_price_extract_enricher(
        sCartItemQuotedPriceList="sCartItemQuotedPriceList",
        sCartItemList="sCartItemList",
        sCartItemCate1IdList="sCartItemCate1IdList",
        sCartItemCate2IdList="sCartItemCate2IdList",
        sCartItemCate3IdList="sCartItemCate3IdList",
    )
    ```
    """
    self._add_processor(MerchantLiveScartPriceExtractEnricher(kwargs))
    return self

  def merchant_live_slice_feature_extract_enricher(self, **kwargs):
    """
    MerchantLiveSliceFeatureExtractEnricher
    -------
    参数
    ------
    `colossus_resp_attr`: [必填][string] 批量的 int 数据输入源。
    `aid_list_attr`: [必填][string] 批量的 int 数据输入源对应的pid。
    `pid_list_attr`: [必填][string] 批量的 int 数据输入源对应的pid。
    `pid_real_list_attr`: [必填][string] 批量的 int 数据输入源对应的pid_real。
    `request_time_attr`: [必填][string] request_time_attr。
    `enable_filter_request_time_attr`: [必填][bool] enable_filter_request_time_attr。
    `filter_offset_ms_attr`: [必填][int] filter_offset_ms_attr。
    调用示例
    ------
    ``` python
    .merchant_live_slice_feature_extract_enricher(
        colossus_resp_attr="slice_colossus_output",
        aid_list_attr="slice_aId_list",
        pid_list_attr="slice_item_key_list",
        pid_real_list_attr="slice_pId_list",
        request_time_attr="request_time_slice",
        enable_filter_request_time_attr=True,
        filter_offset_ms_attr=60 * 3,
    )
    ```
    """
    self._add_processor(MerchantLiveSliceFeatureExtractEnricher(kwargs))
    return self
  
  def merchant_bid_service_strategy_enricher(self, **kwargs):
    """
    StrategyEnricher
    -------
    参数
    ------
    - `photo_store_kconf_key`: [必填][string] common index 格式的索引。
    
    - `strategy_kconf_key`: [必填][string] 策略索引 kconf。
    
    - `item_id_attr`: [必填][string] 索引的 attr 名。
    
    - `common_biz_attr` [必填][string] 存储 biz 的 attr name。
   
    - `common_strategy_ids_attr` [必填][string] ab 命中的策略列表
    
    - `common_flow_mark_strategy_ids_attr` [必填][string] ab 命中的打标策略列表
     
    调用示例
    ------
    ``` python
    merchant_bid_service_strategy_enricher(
      photo_store_kconf_key='xxx.kconf',
      strategy_kconf_key = 'xxx.kconf',
      common_biz_attr = 'MERCHANT_LIVE',
      common_strategy_ids_attr = 'strategy_ids',
      common_flow_mark_strategy_ids_attr = 'flow_mark_strategy_ids',
      item_id_attr = 'aId'
    )
    ```
    """
    self._add_processor(StrategyEnricher(kwargs))
    return self

  def merchant_bid_service_strategy_concat_enricher(self, **kwargs):
    """
    StrategyConcatEnricher
    -------
    参数
    ------
    - `item_id_attrs`: [必填][string] 需要合并的索引的 attr 名列表。
    
    - `common_biz_attr` [必填][string] 存储 biz 的 attr name。
     
    调用示例
    ------
    ``` python
    merchant_bid_service_strategy_concat_enricher(
      common_biz_attr = "biz",
      item_id_attrs = ["aId", "itemId"],
    )
    ```
    """
    self._add_processor(StrategyConcatEnricher(kwargs))
    return self

  def merchant_purchased_filter_enricher(self, **kwargs):
    """
    MerchantPurchasedFilterEnricher
    -------
    参数
    ------
    
    - `strategy_kconf_key`: [必填][string] 策略 kconf。
    
    - `order_seller_attr`: [必填][string] 已购商品seller列表。
   
    - `pay_order_time_attr` [必填][string] 已购商品购买时间列表。
    
    - `seller_id_attr` [必填][string] 主播id 或 店铺id。

    - `cart_item_cate3_attr` [选填][string] 直播挂车商品三级类目列表。

    - `merchant_purchased_filter_flag` [选填][int] 输出已购过滤标记。

    - `merchant_purchased_filter_reason_list` [选填][array string] 输出已购过滤原因列表。
     
    调用示例
    ------
    ``` python
    merchant_purchased_filter_enricher(
        strategy_kconf_key='merchant_purchased_strategy_kconf_path',
        order_seller_attr='colossus_order_seller_id_list',
        pay_order_time_attr='colossus_order_pay_order_time_list',
        seller_id_attr='aId',
        cart_item_cate3_attr='sCartItemCate3IdList',
        merchant_purchased_filter_flag='merchant_purchased_filter_flag',
        merchant_purchased_filter_reason_list='merchant_purchased_filter_reason_list',
    )
    ```
    """
    self._add_processor(MerchantPurchasedFilterEnricher(kwargs))
    return self
  
  def merchant_barrier_arranger(self, **kwargs):
    """
    MerchantItemBarrierArranger
    -------
    对 pass_flags 候选保量，对其余后选进行等比例控量, 最终保留 size_limit 个候选。
    
    [设计文档](https://docs.corp.kuaishou.com/d/home/<USER>

    参数
    ------
    
    - `size_limit`: [必填][int][动态参数] 截断后 item 数量。
    
    - `pass_flags`: [必填][dict] 保送标记；如果保送候选数量大于控量总数，那么也等比例取 top
        
    调用示例
    ------
    ``` python
    merchant_barrier_arranger(
        size_limit=1500,
        pass_flags={
          "is_redirect_flag": 1, # 对重定向保送
        },
    )
    ```
    """
    self._add_processor(MerchantItemBarrierArranger(kwargs))
    
  def merchant_colossus_serialize_enricher(self, **kwargs):
    """
    MerchantColossusSerializeEnricher
    -----
    参数
    -----
    - `input_colossus_attr`: [必填] 通过colossus算子,获取的非pb格式的返回结果
    - `input_colossus_type`: [必填] 对应哪种colossus服务
    - `output_serialize_attr`: [必填] 处理后的string_attr_name
    - `output_serialize_size`: [必填] 处理后的item个数

    调用示例
    ------
    ``` python
    merchant_colossus_serialize_enricher(
      input_colossus_attr = 'colossus_liveItemV4_attr',
      input_colossus_type = 1,
      output_serialize_attr = 'liveItemV4_info',
      output_serialize_size = 'liveItemV4_size',
    )
    ```
    """
    self._add_processor(MerchantColossusSerializeEnricher(kwargs))
    return self
  
  def merchant_colossus_deserialize_enricher(self, **kwargs):
    """
    MerchantColossusDeserializeEnricher
    -----
    参数
    -----
    - `output_colossus_attr`: [必填] 回填的colossus解析结果,所承载的字段
    - `input_colossus_type`: [必填] 对应哪种colossus服务
    - `input_serialize_attr`: [必填] 处理前的string_attr_name
    - `input_serialize_size`: [必填] 处理前的item个数

    调用示例
    ------
    ``` python
    merchant_colossus_deserialize_enricher(
      output_colossus_attr = 'colossus_liveItemV4_attr',
      input_colossus_type = 1,
      input_serialize_attr = 'liveItemV4_info',
      input_serialize_size = 'liveItemV4_size',
    )
    ```
    """
    self._add_processor(MerchantColossusDeserializeEnricher(kwargs))
    return self
  
  def merchant_unify_truncate(self, **kwargs):
    """
    MerchantUnifyTruncateArranger
    -----
    [统一截断、归因和保量算子](https://docs.corp.kuaishou.com/k/home/<USER>/fcACnXxaRNsJGovPVoDPT5pk4?ro=false)

    参数
    -----
    - `primary_score`: [必填][string][动态参数] 排序分数的 item attr name

    - `limit`: [必填][int][动态参数] 截断数量
    
    - `channels`: [必填][dict list] 染色&打标通道: 1.染色和增量打标：越前后染色 reason 优先级越高；2. 
    非增量打标：满足条件就打标。dict 参数：

        * `type`: [必填][string] 通道类型 `score` or `condition`

        * `name`: [必填][string] 通道名称

        * `attr_name` [选填][string] 通道对应的 item attr name

        * `reason` [选填][int][动态参数] 通道对应的 reason

        * `enable` [选填][int][动态参数] 是否生效的 common attr name

        * `select_if` [`type` 为 `condition` 时必填][string] 选择条件: `<` 、 `<=` 、 `==` 、 `>=` 、 `>`

        * `compare_to` [`type` 为 `condition` 时必填][double][动态参数] 比较的值

        * `target_item` [必填][string] item attr name， 用于圈选目标 item

        * `set_attr_value` [选填][dict list] 打标参数

            * `attr_name`: [必填][string] 打标的 attr name

            * `op`: [必填][string] 打标的操作符 `=` 、 `|=`

            * `value`: [必填][int/string] 打标的值

            * `default_val`: [必填][int/string] 打标的值

            * `is_unique`: [选填][bool] 该染色操作是否先对于其它 channel 唯一，默认 False
    
    - `pass_channels`: [必填][dict list] 保量通道； 1. 越靠前优先级越高； 2. 如果 item 已经能透出，不占用替换名额

        * `insert_to`: `head` 插入到最前面；`tail` 插入的最末尾

        * `limit`: [必填][int] 最多替换几个候选

        * `sort_score`: [选填][string] 排序分数的 item attr name, 默认为 `primary_score`

        * 其它参数同 `channels` 的 `type` 为 `condition` 的所有取值

    调用示例
    ------
    ``` python
    merchant_unify_truncate(
      primary_score="traffic_worth_score",
      limit=5,
      channels=[ 
        {
          "type": "score",  # 如果按当前 score 排序不能透出，则染色当前 reason
          "name": "xx_bonus",
          "attr_name": "traffic_worth_score_no_xxx",
          "reason": 1234,
          "set_attr_value": [# 打标
            {"attr_name": "is_xxx_flag", "op": "=", "value": 1, "default_val": 0},
            {"attr_name": "upliftPvFlag", "op": "|=", "value": 1<<10, "default_val": 0}
          ]
        },
        {
          "type": "condition",  # 基于条件染色，e.g.心智染色
          "name": "fe",
          "enable_attr": "enable_unify_truncate_fe_channel", # 支持小流量实验
          "attr_name": "gpm",
          "select_if": "<",
          "compare_to": 1.0,
          "reason": 1235
          "set_attr_value": [# 打标
            ...
          ]
        }
      ],
      pass_channels=[ # 1. 越靠后优先级越高；2. 如果 item 已经能透出，则不替换
        {
          "target_item": "is_xxx",  # 圈选 is_xxx == 1 的候选
          "name": "force_insert",
          "insert_to": "head", # `head` or `tail`
          "attr_name": "gpm",
          "select_if": ">",
          "compare_to": 0.1,
          "reason": 1236,
          "limit": 1, # 最多替换几个候选
          "set_attr_value": [# 打标
            ...
          ]
        }
      ]
    )
    ```
    """
    self._add_processor(MerchantUnifyTruncateArranger(kwargs))
    return self

  def merchant_reason_map(self, **kwargs):
    """
    MerchantReasonMapArranger
    -----
    reason 映射
    -----
    参数
    -----
    - `reason_map`: [必填][dict list] 

        * `key`: [必填][int] 原 reason 值

        * `value`: [必填][int] 映射后的 reason 值

    调用示例
    ------
    ``` python
    merchant_reason_map(
      reason_map=[
        {"key": 1244, "value": 2244},
        {"key": 1245, "value": 2245},
      ]
    )
    ```
    """
    self._add_processor(MerchantReasonMapArranger(kwargs))
    return self

  def merchant_percentile_map_enricher(self, **kwargs):
    """
    MerchantPercentileMapEnricher
    -----
    参数
    ----
    - `convert_item_attrs`: [必填]
    - `converted_item_attrs`: [必填]
    - `statistic_sample_rate`: [选填] default 0.1

    调用示例
    ```python
    merchant_percentile_map_enricher(
        convert_item_attrs = ['ctr', 'cvr'],
        converted_item_attrs = ['ctr_pct', 'cvr_pct'],
        statistic_sample_rate = 0.1,
    )
    ```
    """
    self._add_processor(MerchantPercentileMapEnricher(kwargs))
    return self
  
  def merchant_mix_rank_pack_mmu_embd_enricher(self, **kwargs):
    """
    MerchantMixRankPackMMUEmbdEnricher
    -----
    归并MMUEmbedding

    参数
    -----
    - `from_common_attr`: [必填] item key所在common attr
    - `mmu_embd_attr`: [必填] mmu embedding item attr
    - `output_common_attr`: [必填] 输出common attr
    - `dimension`: [必填] dimension

    调用示例
    -----
    ```python
    .merchant_mix_rank_pack_mmu_embd_enricher(
      from_common_attr='item_key_list',
      mmu_embd_attr='mmu_embedding',
      output_common_attr='pack_mmu_embedding',
      dimension=64
    )
    ```
    """
    self._add_processor(MerchantMixRankPackMMUEmbdEnricher(kwargs))
    return self
  
  def merchant_mix_ad_xtr_enricher(self, **kwargs):
    """
    MerchantMixAdXTREnricher
    -----
    电商混排商城化属性扩展

    参数
    -----
    - `ad_result_attr`: [选填] 填充ad_result的attr名称,默认值为`ad_result`

    调用示例
    -----
    ```python
    .merchant_mix_ad_xtr_enricher(
      ad_result_attr='ad_result',
    )
    ```
    """
    self._add_processor(MerchantMixAdXTREnricher(kwargs))
    return self
  def merchant_get_preview_adinfo_enricher(self, **kwargs):
    """
    MerchantMixAdXTREnricher
    -----
    电商混排商城化属性扩展，货架混排隔离开关

    参数
    -----
    - `ad_result_attr`: [选填] 填充ad_result的attr名称,默认值为`ad_result`

    调用示例
    -----
    ```python
    .merchant_mix_ad_xtr_enricher(
      ad_result_attr='ad_result',
    )
    ```
    """
    self._add_processor(MerchantGetPreviewAdinfoEnricher(kwargs))
    return self

  def merchant_set_adresult_info_enricher(self, **kwargs):
    """
    MerchantSetAdresultInfoEnricher
    -----
    电商混排商城化属性修改

    参数
    -----
    - `ad_result_attr`: [选填] 填充ad_result的attr名称,默认值为`ad_result`

    调用示例
    -----
    ```python
    .merchant_set_adresult_info_enricher(
      ad_result_attr='ad_result',
    )
    ```
    """
    self._add_processor(MerchantSetAdresultInfoEnricher(kwargs))
    return self
  
  def merchant_mix_ad_trace_log_enricher(self, **kwargs):
    """
    MerchantMixAdTraceLogEnricher
    -----
    电商混排填充商业化trace_log

    参数
    -----
    - `ad_result_attr`: [选填] 填充ad_result的attr名称,默认值为`ad_result`
    - `filter_reason_attr`: [选填] 填充过滤原因的attr名称,默认值为`ad_filter_reason`
    - `pos_attr`: [选填] 填充广告位置的attr名称,默认值为`ad_pos`
    - `mix_score_attr`: [选填] 填充混排分的attr名称,默认值为`ad_mix_score`
    - `ad_request_attr`: [选填] 填充ad_request的attr名称,默认值为`ad_request`
    - `output_attr`: [选填] 填充输出trace_log的attr名称,默认值为`ad_mix_rank_trace_log`

    调用示例
    -----
    ```python
    .merchant_mix_ad_trace_log_enricher(
      ad_result_attr = 'ad_result',
      filter_reason_attr = 'ad_filter_reason',
      pos_attr = 'ad_pos',
      mix_score_attr = 'ad_mix_score',
      ad_request_attr = 'ad_request',
      output_attr = 'ad_mix_rank_trace_log'
    )
    ```
    """
    self._add_processor(MerchantMixAdTraceLogEnricher(kwargs))
    return self

  def merchant_mc_percentile_map_enricher(self, **kwargs):
    """
    MerchantMCPercentileMapEnricher
    -----
    参数
    ----
    - `convert_item_attrs`: [必填]
    - `converted_item_attrs`: [必填]
    - `statistic_sample_rate`: [选填] default 0.1

    调用示例
    ```python
    merchant_mc_percentile_map_enricher(
        convert_item_attrs = ['ctr', 'cvr'],
        converted_item_attrs = ['ctr_pct', 'cvr_pct'],
        statistic_sample_rate = 0.1,
    )
    ```
    """
    self._add_processor(MerchantMCPercentileMapEnricher(kwargs))
    return self

  def merchant_slice_feature_colossus_enricher_new(self, **kwargs):
    """
    CommonMerchantSliceFeatureColossusEnricherNew
    ------
    电商直播间切片特征 colossus 解析 

    参数配置
    ------
    `colossus_resp_attr`: [string] colossus_resp_attr

    `aid_list_attr` : [string] aid_list_attr

    `pid_list_attr` : [string] pid_list_attr

    `request_time_attr` : [string] request_time_attr

    `enable_filter_request_time_attr` : [boolean]] enable_filter_request_time_attr

    `filter_offset_ms_attr` : [int] filter_offset_ms_attr

    调用示例
    ------
    ``` python
    .merchant_slice_feature_colossus_enricher_new(
        colossus_resp_attr="colossus_output",
        aid_list_attr="aId_list",
        pid_list_attr="item_key_list",
        request_time_attr="request_time",
        enable_filter_request_time_attr=True,
        filter_offset_ms_attr=60*3
    )
    ```
    """
    self._add_processor(CommonMerchantSliceFeatureColossusEnricherNew(kwargs))
    return self
  
  def merchant_slice_new_feature_extract_enricher(self, **kwargs):
    """
    MerchantSliceNewFeatureExtractEnricher
    ------
    电商直播间切片特征 batch 解析

    参数
    ------
    - `pid_list`: [必填]
    - `input_int_attr`: [必填]

    调用示例
    ------
    ```python
    merchant_slice_new_feature_extract_enricher(
        pid_list="item_key_list",
        input_int_attr="int_output_attrs"
    )
    ```
    """
    self._add_processor(MerchantSliceNewFeatureExtractEnricher(kwargs))
    return self

  def merchant_leafshow_set(self, **kwargs):
    """
    MerchantLeafShowSetEnricher
    ------
    参数
    ------
    - `ttl`: [必填] leafshow_set 的过期时间（s）
    - `kv_str_attr`: [必填] leafshow_set 的 kv_str: key: live_id/aid, value: timestamp
    - `action_type`: [选填] default 0
      - 0: 获取 leafshow_set 到 id_list_attr
      - 1: 将当前结果集添加到 leafshow_set kv_str
    - `id_list_attr`: [选填] 当 action_type = 0 时必填；用于获取 live_id 列表

    调用示例
    ------
    ```python
    merchant_leafshow_set(
        ttl=180,
        key_attr="aId",
        kv_str_attr="redis_value",
        id_list_attr="leafshow_set_aid_list",
    )
    ```
    """
    self._add_processor(MerchantLeafshowSetEnricher(kwargs))
    return self  

  def merchant_intention_extract_enricher(self, **kwargs):
    """
    MerchantIntentionExtractEnricher
    -----
    电商意图特征解析

    参数配置
    ----
    - `request_time_attr`: [必填] 请求时间戳
    - `photo_id_list_attr`: [必填] 用户历史浏览的有电商意图的短视频ID序列
    - `timestamp_list_attr`: [必填] 时间戳序列
    - `score_list_attr`: [必填] 电商意图得分序列
    - `cate1_list_attr`: [必填] 一级类目序列
    - `cate2_list_attr`: [必填] 二级类目序列
    - `cate3_list_attr`: [必填] 三级类目序列
    - `goods_id_list_attr`: [必填] 商品id序列
    - `label_list_attr`: [必填] 短视频标签序列
    - `duration_list_attr`: [选填] 视频时长序列
    - `play_time_list_attr`: [选填] 播放时长序列
    - `tag_list_attr`: [选填]
    - `channel_list_attr`: [选填]

    - `filter_merchant_photo`: [选填] default 0 是否过滤电商短视频
      - 0: 不过滤电商短视频
      - 1: 过滤电商短视频
    - `intention_list_limit_size`: [选填] default 12000 电商意图序列长度上限

    - `photo_goods_iCate1Id_attr`: [必填] 候选item一级类目
    - `photo_goods_iCate2Id_attr`: [必填] 候选item二级类目
    - `photo_goods_iCate2Id_attr`: [必填] 候选item三级类目

    -`resp_photo_id_list_attr`: [必填] 
    -`resp_score_list_attr`: [必填] 
    -`resp_cate1_list_attr`: [必填] 
    -`resp_cate2_list_attr`: [必填] 
    -`resp_cate3_list_attr`: [必填] 
    -`resp_day_lag_list_attr`: [必填] 
    -`resp_goods_id_list_attr`: [必填] 
    -`resp_label_list_attr`: [必填] 
    -`resp_duration_list_attr`: [选填] 
    -`resp_play_time_list_attr`: [选填] 
    -`resp_tag_list_attr`: [选填] 
    -`resp_channel_list_attr`: [选填] 

    调用示例
    ```python
    merchant_intention_extract_enricher(
        request_time_attr="_REQ_TIME_",
        photo_id_list_attr="photo_id_list",
        timestamp_list_attr="timestamp_list",
        score_list_attr="merchant_intention_score_list",
        cate1_list_attr="merchant_intention_cate_1_list",
        cate2_list_attr="merchant_intention_cate_2_list",
        cate3_list_attr="merchnat_intention_cate_3_list",
        goods_id_list_attr="merchant_intention_goods_id_list",
        label_list_attr="label_list",
        duration_list_attr="duration_list",
        play_time_list_attr="play_time_list",
        tag_list_attr="tag_list",
        channel_list_attr="channel_list",
        filter_merchant_photo=0,
        intention_list_limit_size=100,
        photo_goods_iCate1Id_attr="photo_goods_iCate1Id",
        photo_goods_iCate2Id_attr="photo_goods_iCate2Id",
        photo_goods_iCate3Id_attr="photo_goods_iCate3Id",
        resp_photo_id_list_attr="merchant_intention_photo_id_list_related",
        resp_score_list_attr="merchant_intention_score_list_related",
        resp_cate1_list_attr="merchant_intention_cate_1_list_related",
        resp_cate2_list_attr="merchant_intention_cate_2_list_related",
        resp_cate3_list_attr="merchant_intention_cate_3_list_related",
        resp_day_lag_list_attr="merchant_intention_day_lag_list_related",
        resp_goods_id_list_attr="merchant_intention_goods_id_list_related",
        resp_label_list_attr="merchant_intention_label_list_related",
        resp_duration_list_attr="merchant_intention_duration_list_related",
        resp_play_time_list_attr="merchant_intention_play_time_list_related",
        resp_tag_list_attr="merchant_intention_tag_list_related",
        resp_channel_list_attr="merchant_intention_channel_list_related"
    )
    ```
    """
    self._add_processor(MerchantIntentionExtractEnricher(kwargs))
    return self
  def merchant_perflog_reason_count(self, **kwargs):
    """
    MerchantRecoReasonCountPerflogObserver
    ------
    统计当前各个 item reason 增量（不存在重叠）和全量（重叠时分别计数）数量，上报 perflog 并可在 [grafana 监控](https://grafana.corp.kuaishou.com/d/S10M9QdZk/commonleafjian-kong?orgId=3&panelId=275&fullscreen)中查看统计结果

    注意：如果要监测多个点，请用 `check_point` 区分，否则会合并统计数据

    参数配置
    ------
    `check_point`: [string] [动态参数] 自定义打点位置标识，将用于 perflog 聚合和 grafana 展示

    `log_info_from_attr`: [string] 选配项，需要额外上报的 common attr 信息，仅支持 int/string 类型。

    `reason_list`: [string] 必配项，去重后得到的 reason_list。

    调用示例
    ------
    ``` python
    # 统计 default 流程中的召回后 reason 分布
    .perflog_reason_count(check_point="default.retrieval")
    ```
    """
    self._add_processor(MerchantRecoReasonCountPerflogObserver(kwargs))
    return self
  
  def distributed_cache_xtr_score_reader(self, **kwargs):
    """
    MerchantDistributedCacheXtrScoreReaderEnricher
    --------

    参数
    ------
    `photo_store_kconf_key`: [string]必填 CacheStore配置，使用dynamic_photo_store
    
    `is_cache_hit`: [string] item_attr name，cache命中标记名，命中填1(int)，未命中不填，默认起名request_type + "_default_hit_cache"

    `table_kconf_path` : [string] cache服务table metainfo的kconf路径

    `table_name` : [string] 业务对应table_name
    
    `no_overwrite`: [bool] 是否不覆盖已存在的 item_attr, 默认 False

    `attr_name_types`: [list] 读取的attr_name 及其对应的 type, 格式为 { "name": "author_id", "type": "int" }, 支持的type类型为: double, int, string. 注意： 要保证attr_name+前缀已写入table_kconf_path

    `get_table_name_from_kconf`: [bool] 选配项 是否从 kconf 中获取 table name, 默认 False

    `use_request_type`: [bool] 选配项 是否使用 request_type 作为 table name 的前缀, 默认 True

    `partition_with_uid`: [bool] 选配项 是否让同一个 uid 的 item 在一个 batch, 从而优化 batch size; 默认 False, 默认使用 hash(uid + item_id) 作为分区键, 这种情况下 batch size 基本是 1

    调用示例
    ------
    ``` python
    .distributed_cache_xtr_score_reader(
      photo_store_kconf_key = "reco.distributedIndex.XXXXX",
      table_kconf_path = "reco.merchant.MallDistributedCacheTableInfo",
      table_name = "mall-cache-live",
      is_cache_hit = "is_cache_hit_flag",
      attr_name_types = [
        { "name": "author_id", "type": "int" },
        { "name": "pl_ctr", "type": "double" },
      ]
    )
    ```
    """
    self._add_processor(MerchantDistributedCacheXtrScoreReaderEnricher(kwargs))
    return self

  def retrieve_by_uni_recall_distributed_cache(self, **kwargs):
    """
    MerchantCommonDistributedCacheReaderRetriever
    --------

    参数
    ------
    `photo_store_kconf_key`: [string]必填 CacheStore配置，使用dynamic_photo_store

    `table_kconf_path` : [string] cache服务table metainfo的kconf路径

    `table_name` : [string] 业务对应table_name

    `attr_name_types`: [list] 读取的attr_name 及其对应的 type, 格式为 { "name": "author_id", "type": "int" }, 支持的type类型为: double, int, string. 注意： 要保证attr_name+前缀已写入table_kconf_path

    调用示例
    ------
    ``` python
    .retrieve_by_uni_recall_distributed_cache(
      photo_store_kconf_key = "reco.distributedIndex.XXXXX",
      table_kconf_path = "reco.merchant.MallDistributedCacheTableInfo",
      table_name = "mall-cache-live",
      attr_name_types = [
        { "name": "item_id", "type": "int" },
        { "name": "reason", "type": "int" },
      ]
    )
    ```
    """
    self._add_processor(MerchantCommonDistributedCacheReaderRetriever(kwargs))
    return self

  def merchant_fetch_tower_dot_product_pxtr(self, **kwargs):
    """
    TowerFetchDotProductAttrEnricher
    ------
    向远端请求预估多个label (or target) 的 user * item 的pxtr

    发item_embedding_key_attr送user embedding 和 item key 列表, 获取各item对应label下的user * item 的pxtr

    user embedding : 每个label都有独有的user embedding，多个label的user embedding拼接成一个更长的embedding

    参数
    ------
    `kess_service`: [string] 预估服务kess 服务名

    `kess_cluster`: [string] 预估服务kess 集群名，默认 PRODUCTION

    `shards`: [int] 预估服务shard 数量

    `timeout_ms`: [int] 预估服务 rpc 超时，单位毫秒，默认 50

    `user_embedding_attr`: [string] user embedding 在 context CommonAttr 中的字段名

    `use_item_key_as_embed_key`: [bool] true: item_key 当做 embedding key,  fasle: 从 ItemAttr 获取 embedding key

    `item_embedding_key_attr`: [string] use_item_key_as_embed_key 为 false 时，从 ItemAttr 获取 embedding key 列表的key

    `predict_labels`: [string list] 与预估的label名称列表

    `server_request_type`: [string] 预估服务也是dragonfly 实现，需要request_type参数

    `req_common_embedding_attr`: [string] 从 CommonAttr 中指示 common embedding 存放的字段,默认值req_common_embedding

    `req_tower_caller_attr`: [string] 在 request common attr 中标明自己的调用身份，默认值tower_caller

    `return_pxtr_value_attr`: [string] 在 request common attr 中指定返回 pxtr 结果时存放的 response common attr字段，被调必须遵守，默认值return_pxtr_value
                              同时，如果上游没有指定 pxtr 值返回的字段(item_pxtr_value_attr)，那么该字段也是返回给上游的 attr 字段名

    `output_type`: [int]  0: 以 `std::vector<float>*` 输出至 common attr （默认）
                          1: 以 Doublelist 输出至 item attr
                          2: 以 Double 输出至对应 pxtr 的 item attr

    `sub_req_num_in_shard`: [int] 将 shard 请求拆分成若干并行请求的自请求，默认为 1

    `kconf_timeout_ms_attr`: [int] kconf 上配置的 pxtr 服务访问超时阈值

    `item_hetu_tag_attr`: [string] item hetu tag 在 context item attr 中的字段名 ， 可选

    `total_hetu_tag_num`: [int] 所有 hetu tag 种类的数量 , 可选

    `item_pxtr_label_attr`: [string] 输出 pxtr label 的 attr 名，默认为 "return_pxtr_value"

    `item_pxtr_value_attr`: [string] 输出 pxtr value 的 attr 名，默认同 `return_pxtr_value_attr`

    示例
    ------
    ``` python
    .fetch_tower_dot_product_pxtr(user_embedding_attr = "user_output_embedding",
                                  use_item_key_as_item_embed_key = True,
                                  predict_labels = [ctr, ltr, svtr],
                                  kess_service = 'grpc_TowerPhotoEmbeddingWithPxtrCalc',
                                  shards = 8,
                                  timeout_ms = 20,
                                  server_request_type = 'rt_tower_predict_pxtr',
                                  req_common_embedding_attr='req_common_embedding',
                                  req_tower_caller_attr='tower_caller',
                                  return_pxtr_value_attr='return_pxtr_value_attr',
                                  )
    ```

    """
    self._add_processor(MerchantTowerFetchDotProductAttrEnricher(kwargs))
    return self

  def merchant_cate_sim_extract_enricher(self, **kwargs):
    """
    MerchantCateSimExtractEnricher
    -----
    电商长序列 cate sim 特征解析

    参数配置
    ----
    - `request_time_attr`: [必填] 请求时间戳
    - `photo_id_list_attr`: [必填] 用户历史序列
    - `seller_id_list_attr`: [必填] 作者序列
    - `real_seller_id_list_attr`: [必填] 作者序列
    - `timestamp_list_attr`: [必填] 时间戳序列
    - `category_list_attr`: [必填] 类目序列
    - `flow_type_list_attr`: [必填] 流量类型序列
    - `from_list_attr`: [必填] 来源序列
    - `price_list_attr`: [必填] 商品价格序列
    - `good_detail_page_view_time_list_attr`: [必填] 商品详情浏览时长序列
    - `good_origin_price_list_attr`: [必填] 商品原始价格序列
    - `label_list_attr`: [必填] 短视频标签序列
    
    - `filt_type`: [必填] default 0 cate过滤类型
      - 0: cate1 sim 过滤电商短视频
      - 1: cate2 sim 过滤电商短视频
      - 2: cate3 sim 过滤电商短视频
    - `list_limit_size`: [选填] default 1000, 序列长度上限
    - `time_threshold`: [选填] default 180, 距今间隔过滤

    - `photo_goods_iCateXId_attr`: [选填] 候选item X级类目

    -`resp_item_id_list_attr`: [必填] 
    -`resp_aid_list_attr`: [必填] 
    -`resp_cate1_list_attr`: [必填] 
    -`resp_cate2_list_attr`: [必填] 
    -`resp_cate3_list_attr`: [必填] 
    -`resp_view_lag_list_attr`: [必填] 
    -`resp_seller_id_list_attr`: [必填] 
    -`resp_carry_type_list_attr`: [必填] 
    -`resp_click_type_list_attr`: [必填] 
    -`resp_click_from_list_attr`: [必填]
    -`resp_price_list_attr`: [必填] 
    -`resp_origin_price_list_attr`: [必填] 
    -`resp_price_diff_list_attr`: [必填] 
    -`resp_page_view_time_attr`: [必填] 
    -`resp_label_list_attr`: [必填] 

    调用示例
    ```python
    merchant_cate_sim_extract_enricher(
        request_time_attr="_REQ_TIME_",
        photo_id_list_attr="good_click_item_id_list",
        seller_id_list_attr="good_click_seller_id_list",
        real_seller_id_list_attr="good_click_real_seller_id_list",
        timestamp_list_attr="good_click_timestamp_list",
        category_list_attr="good_click_category_list",
        flow_type_list_attr="good_click_flow_type_list",
        from_list_attr="good_click_from_list",
        price_list_attr="good_click_price_list",
        good_detail_page_view_time_list_attr="good_detail_page_view_time_list",
        good_origin_price_list_attr="good_click_origin_price_list",
        label_list_attr="good_click_label_list",
        filt_type=0,
        list_limit_size=1000,
        photo_goods_iCateXId_attr="photo_goods_iCate1Id",
        resp_item_id_list_attr="good_click_cate1_sim_item_id_list",
        resp_aid_list_attr="good_click_cate1_sim_aid_list",
        resp_cate1_list_attr="good_click_cate1_sim_cate1_list",
        resp_cate2_list_attr="good_click_cate1_sim_cate2_list",
        resp_cate3_list_attr="good_click_cate1_sim_cate3_list",
        resp_view_lag_list_attr="good_click_cate1_sim_lag_list",
        resp_seller_id_list_attr="good_click_cate1_sim_seller_id_list",
        resp_carry_type_list_attr="good_click_cate1_sim_carry_type_list",
        resp_click_type_list_attr="good_click_cate1_sim_click_type_list",
        resp_click_from_list_attr="good_click_cate1_sim_click_from_list",
        resp_price_list_attr="good_click_cate1_sim_price_list",
        resp_origin_price_list_attr="good_click_cate1_sim_origin_price_list",
        resp_price_diff_list_attr="good_click_cate1_sim_price_diff_list",
        resp_page_view_time_attr="good_click_cate1_sim_page_view_time_list",
        resp_label_list_attr="good_click_cate1_sim_label_list"
    )
    ```
    """
    self._add_processor(MerchantCateSimExtractEnricher(kwargs))
    return self
  
  def merchant_long_view_extract_enricher(self, **kwargs):
    """
    MerchantLongViewExtractEnricher
    -----
    电商长序列个人主页 long view 特征解析

    参数配置
    ----
    # input common attr
    - `request_time_attr`: [必填] 请求时间戳
    - `photo_id_list_attr`: [必填] 用户历史序列
    - `seller_id_list_attr`: [必填] 作者序列
    - `real_seller_id_list_attr`: [必填] 作者序列
    - `timestamp_list_attr`: [必填] 时间戳序列
    - `category_list_attr`: [必填] 类目序列
    - `flow_type_list_attr`: [必填] 流量类型序列
    - `from_list_attr`: [必填] 来源序列
    - `price_list_attr`: [必填] 商品价格序列
    - `good_detail_page_view_time_list_attr`: [必填] 商品详情浏览时长序列
    - `good_origin_price_list_attr`: [必填] 商品原始价格序列
    - `label_list_attr`: [必填] 短视频标签序列
    
    - `long_view_time_threshold`: [选填] default 0, long view 浏览时长门槛
    - `list_limit_size`: [选填] default 1000, 序列长度上限
    # output common attr
    -`resp_item_id_list_attr`: [必填] 
    -`resp_aid_list_attr`: [必填] 
    -`resp_cate1_list_attr`: [必填] 
    -`resp_cate2_list_attr`: [必填] 
    -`resp_cate3_list_attr`: [必填] 
    -`resp_view_lag_list_attr`: [必填] 
    -`resp_seller_id_list_attr`: [必填] 
    -`resp_carry_type_list_attr`: [必填] 
    -`resp_click_type_list_attr`: [必填] 
    -`resp_click_from_list_attr`: [必填]
    -`resp_price_list_attr`: [必填] 
    -`resp_origin_price_list_attr`: [必填] 
    -`resp_price_diff_list_attr`: [必填] 
    -`resp_page_view_time_attr`: [必填] 
    -`resp_label_list_attr`: [必填] 

    调用示例
    ```python
    merchant_long_view_extract_enricher(
        request_time_attr="_REQ_TIME_",
        photo_id_list_attr="good_click_item_id_list",
        seller_id_list_attr="good_click_seller_id_list",
        real_seller_id_list_attr="good_click_real_seller_id_list",
        timestamp_list_attr="good_click_timestamp_list",
        category_list_attr="good_click_category_list",
        flow_type_list_attr="good_click_flow_type_list",
        from_list_attr="good_click_from_list",
        price_list_attr="good_click_price_list",
        good_detail_page_view_time_list_attr="good_detail_page_view_time_list",
        good_origin_price_list_attr="good_click_origin_price_list",
        label_list_attr="good_click_label_list",
        list_limit_size=1000,
        resp_item_id_list_attr="good_click_long_view_item_id_list",
        resp_aid_list_attr="good_click_long_view_aid_list",
        resp_cate1_list_attr="good_click_long_view_cate1_list",
        resp_cate2_list_attr="good_click_long_view_cate2_list",
        resp_cate3_list_attr="good_click_long_view_cate3_list",
        resp_view_lag_list_attr="good_click_long_view_lag_list",
        resp_seller_id_list_attr="good_click_long_view_seller_id_list",
        resp_carry_type_list_attr="good_click_long_view_carry_type_list",
        resp_click_type_list_attr="good_click_long_view_click_type_list",
        resp_click_from_list_attr="good_click_long_view_click_from_list",
        resp_price_list_attr="good_click_long_view_price_list",
        resp_origin_price_list_attr="good_click_long_view_origin_price_list",
        resp_price_diff_list_attr="good_click_long_view_price_diff_list",
        resp_page_view_time_attr="good_click_long_view_page_view_time_list",
        resp_label_list_attr="good_click_long_view_label_list"
    )
    ```
    """
    self._add_processor(MerchantLongViewExtractEnricher(kwargs))
    return self

  def merchant_long_session_extract_enricher(self, **kwargs):
    """
    MerchantLongSessionExtractEnricher
    -----
    电商长序列个人主页 long view 特征解析

    参数配置
    ----
    # input common attr
    - `request_time_attr`: [必填] 请求时间戳
    - `photo_id_list_attr`: [必填] 用户历史序列
    - `aid_list_attr`: [必填] 作者序列
    - `duration_list_attr`: [必填] 物理时长序列
    - `play_time_list_attr`: [必填] 播放时长序列
    - `channel_list_attr`: [必填] channel序列
    - `timestamp_list_attr`: [必填] 时间戳序列
    - `label_list_attr`: [必填] 短视频标签序列
    - `spu_id_list_attr`: [必填] spu id 序列
    - `category_list_attr`: [必填] 短视频类目序列
    - `tag_list_attr`: [必填] 短视频tag序列
    
    - `time_threshold`: [选填] default 5, 浏览时长门槛
    - `list_limit_size`: [选填] default 1000, 序列长度上限
    - `lag_day_threshold`: [选填] default 180, 最大回溯时间上限

    # output common attr
    -`resp_item_id_list_attr`: [必填] 
    -`resp_aid_list_attr`: [必填] 
    -`resp_play_time_list_attr`: [必填] 
    -`resp_duration_list_attr`: [必填]
    -`resp_channel_list_attr`: [必填]
    -`resp_label_list_attr`: [必填]
    -`resp_spu_id_list_attr`: [必填]
    -`resp_cate1_list_attr`: [必填] 
    -`resp_cate2_list_attr`: [必填] 
    -`resp_cate3_list_attr`: [必填] 
    -`resp_lag_day_list_attr`: [必填] 
    -`resp_lag_hour_list_attr`: [必填] 
    -`resp_play_percent_list_attr`: [必填] 
    -`resp_is_click_cart_list_attr`: [选填] 
    -`resp_is_order_list_attr`: [选填] 
    -`resp_is_interaction_list_attr`: [选填]
    -`resp_is_living_list_attr`: [选填] 
    -`resp_is_enter_live_list_attr`: [选填] 
    -`resp_tag_list_attr`: [选填] 
    -`resp_is_like_list_attr`: [选填] 
    -`resp_is_follow_list_attr`: [选填] 
    -`resp_is_forward_list_attr`: [选填] 
    -`resp_is_comment_stay_list_attr`: [选填] 
    -`resp_is_enter_profile_list_attr`: [选填] 
    -`resp_is_commertial_intent_list_attr`: [选填] 
    -`resp_commertial_item_id_list_attr`: [选填] 
    -`resp_commertial_aid_list_attr`: [选填] 
    -`resp_commertial_play_time_list_attr`: [选填] 
    -`resp_commertial_duration_list_attr`: [选填]
    -`resp_commertial_channel_list_attr`: [选填]
    -`resp_commertial_label_list_attr`: [选填]
    -`resp_commertial_spu_id_list_attr`: [选填]
    -`resp_commertial_cate1_list_attr`: [选填] 
    -`resp_commertial_cate2_list_attr`: [选填] 
    -`resp_commertial_cate3_list_attr`: [选填] 
    -`resp_commertial_lag_day_list_attr`: [选填] 
    -`resp_commertial_lag_hour_list_attr`: [选填] 
    -`resp_commertial_play_percent_list_attr`: [选填] 
    -`resp_commertial_tag_list_attr`: [选填] 
    -`resp_commertial_is_like_list_attr`: [选填] 
    -`resp_commertial_is_follow_list_attr`: [选填] 
    -`resp_commertial_is_forward_list_attr`: [选填] 
    -`resp_commertial_is_comment_stay_list_attr`: [选填] 
    -`resp_commertial_is_enter_profile_list_attr`: [选填] 
    -`resp_commertial_is_commertial_intent_list_attr`: [选填] 

    调用示例
    ```python
    merchant_long_session_extract_enricher(
        request_time_attr="_REQ_TIME_",
        photo_id_list_attr="good_click_item_id_list",
        seller_id_list_attr="good_click_seller_id_list",
        real_seller_id_list_attr="good_click_real_seller_id_list",
        timestamp_list_attr="good_click_timestamp_list",
        category_list_attr="good_click_category_list",
        flow_type_list_attr="good_click_flow_type_list",
        from_list_attr="good_click_from_list",
        price_list_attr="good_click_price_list",
        good_detail_page_view_time_list_attr="good_detail_page_view_time_list",
        good_origin_price_list_attr="good_click_origin_price_list",
        label_list_attr="good_click_label_list",
        list_limit_size=1000,
        resp_item_id_list_attr="good_click_long_view_item_id_list",
        resp_aid_list_attr="good_click_long_view_aid_list",
        resp_cate1_list_attr="good_click_long_view_cate1_list",
        resp_cate2_list_attr="good_click_long_view_cate2_list",
        resp_cate3_list_attr="good_click_long_view_cate3_list",
        resp_view_lag_list_attr="good_click_long_view_lag_list",
        resp_seller_id_list_attr="good_click_long_view_seller_id_list",
        resp_carry_type_list_attr="good_click_long_view_carry_type_list",
        resp_click_type_list_attr="good_click_long_view_click_type_list",
        resp_click_from_list_attr="good_click_long_view_click_from_list",
        resp_price_list_attr="good_click_long_view_price_list",
        resp_origin_price_list_attr="good_click_long_view_origin_price_list",
        resp_price_diff_list_attr="good_click_long_view_price_diff_list",
        resp_page_view_time_attr="good_click_long_view_page_view_time_list",
        resp_label_list_attr="good_click_long_view_label_list"
    )
    ```
    """
    self._add_processor(MerchantLongSessionExtractEnricher(kwargs))
    return self

  def distributed_cache_xtr_score_writer(self, **kwargs):
    """
    MerchantDistributedCacheXtrScoreWriterObserver
    -------
    
     参数
    ------
    `kconf_path`: [string] kconf路径名

    `table_name`: [string] table name

    `expire_seconds_attr`: [string] 选配项 item-xtr 的过期时间 item attr, 单位 s, 默认不改变过期时间

    `get_table_name_from_kconf`: [bool] 选配项 是否从 kconf 中获取 table name, 默认 False

    `photo_store_kconf_key`: [string] photo_store 配置 kconf, get_table_name_from_kconf 为 True 时必填

    `use_request_type`: [bool] 选配项 是否使用 request_type 作为 table name 的前缀, 默认 True

    `partition_with_uid`: [bool] 选配项 是否让同一个 uid 的 item 在一个 batch, 从而优化 batch size; 默认 False, 默认使用 hash(uid + item_id) 作为分区键, 这种情况下 batch size 基本是 1

    调用示例
    ------
    ``` python
    .distributed_cache_xtr_score_writer(
      kconf_path = "",
      table_name = "",
    )
    ```
    """
    self._add_processor(MerchantDistributedCacheXtrScoreWriterObserver(kwargs))
    return self

  def uni_recall_distributed_cache_writer(self, **kwargs):
    """
    MerchantCommonDistributedCacheWriterObserver
    -------
    
     参数
    ------
    `kconf_path`: [string] cache服务table meta info的kconf路径

    `table_name`: [string] table name, kconf中必须存在

    调用示例
    ------
    ``` python
    .distributed_cache_xtr_score_writer(
      kconf_path = "reco.distributedIndex.UniRecallDistributedCacheTableInfo",
      table_name = "uni-recall-mall-goods-cache",
    )
    ```
    """
    self._add_processor(MerchantCommonDistributedCacheWriterObserver(kwargs))
    return self
  
  def merchant_full_recall_consume_partition(self, **kwargs):
    """
    MerchantFullRecallConsumePartitionArranger
    ------
    电商全库打分runner端数据消费分区算子 

    参数配置
    ------
    调用示例
    ------
    ``` python
    .merchant_full_recall_consume_partition(
       name="consume_partition" 
    )
    ```
    """
    self._add_processor(MerchantFullRecallConsumePartitionArranger(kwargs))
    return self
  
  def extract_sign_for_item(self, **kwargs):
    """
    ExtractSignProcessorEnricher
    ------
    """
    self._add_processor(ExtractSignProcessorEnricher(kwargs))
    return self

  def merchant_redis_incrby(self, **kwargs):
    """
    MerchantRedisIncrbyObserver
    ------
    调用 redis Incr 命令

    参数配置
    ------
    `redis_cluster`: [string] 访问的 redis 集群

    `keys`: [list] 动态参数, redis key 列表

    `vals`: [list] 动态参数, redis amount 列表

    `expire_s`: [int] 动态参数 选配项 redis key 的过期时间，单位 s, 默认不改变过期时间

    `num`: [int] 动态参数 增加的数目，可缺省，默认为1

    `use_optimized_write`: [int] 动态参数，选配项，使用进程粒度秒级 key 无锁聚合。默认 0 关闭, 1 打开。

    调用示例
    ------
    ``` python
    .merchant_redis_incrby(
      redis_cluster="redisCluster"
      keys=["{{key1}}", "key2"],
      vals=[1, "{{val2}}"],
    )
    ```
    """
    self._add_processor(MerchantRedisIncrbyObserver(kwargs))
    return self


  
  def eshop_video_gsu_extract_enricher(self, **kwargs):
    """
    EshopVideoGsuExtractEnricher
    -----
    挂车短视频序列检索 for 电商1pp

    参数配置
    ----
    - `request_time`: [必填] 请求时间戳
    - `input_eshop_video_photo_id`: [必填] 
    - `input_eshop_video_author_id`: [必填] 
    - `input_eshop_video_play_time`: [必填] 
    - `input_eshop_video_duration`: [必填] 
    - `input_eshop_video_channel`: [必填] 
    - `input_eshop_video_label`: [必填] 
    - `input_eshop_video_timestamp`: [必填] 
    - `input_eshop_video_spu_id`: [必填] 
    - `input_eshop_video_category`: [必填] 
    - `input_aId`: [必填] 
    - `input_sCartItemCate1IdList`: [必填] 
    - `input_sCartItemCate2IdList`: [必填] 
    - `input_sCartItemCate3IdList`: [必填] 
    - `output_eshop_video_gsu_photo_id`: [必填] 
    - `output_eshop_video_gsu_author_id`: [必填] 
    - `output_eshop_video_gsu_play_time`: [必填] 
    - `output_eshop_video_gsu_duration`: [必填] 
    - `output_eshop_video_gsu_channel`: [必填] 
    - `output_eshop_video_gsu_label`: [必填] 
    - `output_eshop_video_gsu_lag`: [必填] 
    - `output_eshop_video_gsu_spu_id`: [必填] 
    - `output_eshop_video_gsu_cate1`: [必填] 
    - `output_eshop_video_gsu_cate2`: [必填] 
    - `output_eshop_video_gsu_cate3`: [必填] 
    - `output_eshop_video_match_aid_mcnt`: [必填] 
    - `output_eshop_video_match_cartcate3_mcnt`: [必填] 
    - `output_eshop_video_match_cartcate2_mcnt`: [必填] 
    - `output_eshop_video_match_cartcate1_mcnt`: [必填] 

    调用示例
    ```python
    eshop_video_gsu_extract_enricher(
        request_time="request_time",
        input_eshop_video_photo_id="eshop_video_photo_id",
        input_eshop_video_author_id="eshop_video_author_id",
        input_eshop_video_play_time="eshop_video_play_time",
        input_eshop_video_duration="eshop_video_duration",
        input_eshop_video_channel="eshop_video_channel",
        input_eshop_video_label="eshop_video_label",
        input_eshop_video_timestamp="eshop_video_timestamp",
        input_eshop_video_spu_id="eshop_video_spu_id",
        input_eshop_video_category="eshop_video_category",
        input_aId="aId",
        input_sCartItemCate1IdList="sCartItemCate1IdList",
        input_sCartItemCate2IdList="sCartItemCate2IdList",
        input_sCartItemCate3IdList="sCartItemCate3IdList",
        output_eshop_video_gsu_photo_id="eshop_video_gsu_merge_photo_id",
        output_eshop_video_gsu_author_id="eshop_video_gsu_merge_author_id",
        output_eshop_video_gsu_play_time="eshop_video_gsu_merge_play_time",
        output_eshop_video_gsu_duration="eshop_video_gsu_merge_duration",
        output_eshop_video_gsu_channel="eshop_video_gsu_merge_channel",
        output_eshop_video_gsu_label="eshop_video_gsu_merge_label",
        output_eshop_video_gsu_lag="eshop_video_gsu_merge_lag",
        output_eshop_video_gsu_spu_id="eshop_video_gsu_merge_spu_id",
        output_eshop_video_gsu_cate1="eshop_video_gsu_merge_cate1",
        output_eshop_video_gsu_cate2="eshop_video_gsu_merge_cate2",
        output_eshop_video_gsu_cate3="eshop_video_gsu_merge_cate3",
        output_eshop_video_match_aid_mcnt="eshop_video_match_aid_mcnt",
        output_eshop_video_match_cartcate3_mcnt="eshop_video_match_cate3_mcnt",
        output_eshop_video_match_cartcate2_mcnt="eshop_video_match_cate2_mcnt",
        output_eshop_video_match_cartcate1_mcnt="eshop_video_match_cate1_mcnt",
    )
    ```
    """
    self._add_processor(EshopVideoGsuExtractEnricher(kwargs))
    return self
  
  def video_merchant_intention_extract_enricher(self, **kwargs):
    """
    VideoMerchantIntentionExtractEnricher
    -----
    短视频序列电商意图检索 for 电商1pp

    参数配置
    ----
    - `request_time`: [必填] 请求时间戳
    - `input_video_photo_id`: [必填] 
    - `input_video_author_id`: [必填] 
    - `input_video_play_time`: [必填] 
    - `input_video_duration`: [必填] 
    - `input_video_channel`: [必填] 
    - `input_video_label`: [必填] 
    - `input_video_timestamp`: [必填] 
    - `input_video_tag`: [必填] 
    - `input_video_merchant_intention_score_v3`: [必填] 
    - `input_video_merchant_intention_cate_1`: [必填] 
    - `input_video_merchant_intention_cate_2`: [必填] 
    - `input_video_merchant_intention_cate_3`: [必填] 
    - `input_video_merchant_intention_cate_leaf`: [必填] 
    - `input_video_merchant_intention_goods_id`: [必填] 
    - `input_aId`: [必填] 
    - `input_sCartItemCate1IdList`: [必填] 
    - `input_sCartItemCate2IdList`: [必填] 
    - `input_sCartItemCate3IdList`: [必填] 
    - `input_sCartItemCategoryLeafIdList`: [必填] 
    - `output_video_gsu_merge_photo_id`: [必填] 
    - `output_video_gsu_merge_author_id`: [必填] 
    - `output_video_gsu_merge_play_time`: [必填] 
    - `output_video_gsu_merge_duration`: [必填] 
    - `output_video_gsu_merge_channel`: [必填] 
    - `output_video_gsu_merge_label`: [必填] 
    - `output_video_gsu_merge_lag`: [必填] 
    - `output_video_gsu_merge_tag`: [必填] 
    - `output_video_gsu_merge_merchant_intention_score_v3`: [必填] 
    - `output_video_gsu_merge_merchant_intention_cate_1`: [必填] 
    - `output_video_gsu_merge_merchant_intention_cate_2`: [必填] 
    - `output_video_gsu_merge_merchant_intention_cate_3`: [必填] 
    - `output_video_gsu_merge_merchant_intention_cate_leaf`: [必填] 
    - `output_video_gsu_merge_merchant_intention_goods_id`: [必填] 
    - `output_video_match_aid_score_mcnt`: [必填] 
    - `output_video_match_cateleaf_mcnt`: [必填] 
    - `output_video_match_cate3_mcnt`: [必填] 
    - `output_video_match_cate2_mcnt`: [必填] 
    - `output_video_match_cate1_mcnt`: [必填] 
    - `output_video_match_score_mcnt`: [必填] 

    调用示例
    ```python
    video_merchant_intention_extract_enricher(
        request_time="request_time",
        input_video_photo_id="video_photo_id",
        input_video_author_id="video_author_id",
        input_video_play_time="video_play_time",
        input_video_duration="video_duration",
        input_video_channel="video_channel",
        input_video_label="video_label",
        input_video_timestamp="video_timestamp",
        input_video_tag="video_tag",
        input_video_merchant_intention_score_v3="video_merchant_intention_score_v3",
        input_video_merchant_intention_cate_1="video_merchant_intention_cate_1",
        input_video_merchant_intention_cate_2="video_merchant_intention_cate_2",
        input_video_merchant_intention_cate_3="video_merchant_intention_cate_3",
        input_video_merchant_intention_cate_leaf="video_merchant_intention_cate_leaf",
        input_video_merchant_intention_goods_id="video_merchant_intention_goods_id",
        input_aId="aId",
        input_sCartItemCate1IdList="sCartItemCate1IdList",
        input_sCartItemCate2IdList="sCartItemCate2IdList",
        input_sCartItemCate3IdList="sCartItemCate3IdList",
        input_sCartItemCategoryLeafIdList="sCartItemCategoryLeafIdList",
        output_video_gsu_merge_photo_id="video_gsu_merge_photo_id",
        output_video_gsu_merge_author_id="video_gsu_merge_author_id",
        output_video_gsu_merge_play_time="video_gsu_merge_play_time",
        output_video_gsu_merge_duration="video_gsu_merge_duration",
        output_video_gsu_merge_channel="video_gsu_merge_channel",
        output_video_gsu_merge_label="video_gsu_merge_label",
        output_video_gsu_merge_lag="video_gsu_merge_lag",
        output_video_gsu_merge_tag="video_gsu_merge_tag",
        output_video_gsu_merge_merchant_intention_score_v3="video_gsu_merge_merchant_intention_score_v3",
        output_video_gsu_merge_merchant_intention_cate_1="video_gsu_merge_merchant_intention_cate_1",
        output_video_gsu_merge_merchant_intention_cate_2="video_gsu_merge_merchant_intention_cate_2",
        output_video_gsu_merge_merchant_intention_cate_3="video_gsu_merge_merchant_intention_cate_3",
        output_video_gsu_merge_merchant_intention_cate_leaf="video_gsu_merge_merchant_intention_cate_leaf",
        output_video_gsu_merge_merchant_intention_goods_id="video_gsu_merge_merchant_intention_goods_id",
        output_video_match_aid_score_mcnt="video_match_aid_score_mcnt",
        output_video_match_cateleaf_mcnt="video_match_cateleaf_mcnt",
        output_video_match_cate3_mcnt="video_match_cate3_mcnt",
        output_video_match_cate2_mcnt="video_match_cate2_mcnt",
        output_video_match_cate1_mcnt="video_match_cate1_mcnt",
        output_video_match_score_mcnt="video_match_score_mcnt",
    )
    ```
    """
    self._add_processor(VideoMerchantIntentionExtractEnricher(kwargs))
    return self
  
  def merchant_frequency_sampling_enricher(self, **kwargs):
    """
    MerchantFrequencySamplingEnricher
    ------
    样本按频率采样

    参数配置
    ------
    `buffer_size`: [int] buffer_size

    `default_sample_rate_min`: [double] default_sample_rate_min
    
    `default_sample_rate_max`: [double] default_sample_rate_max

    `sample_flag_attr`: [string] 动态参数, 输入 sample_flag_attr
    
    `item_keep_flag_attr`: [string] 动态参数, 输出 item_keep_flag_attr
    
    `real_sample_rate_attr`: [string] 动态参数, 输出 real_sample_rate_attr

    调用示例
    ------
    ``` python
    .merchant_frequency_sampling_enricher(
      buffer_size_ = 50000,
      default_sample_rate_min = 0.1,
      default_sample_rate_max = 0.5,
      sample_flag_attr = 'sample_flag',
      item_keep_flag_attr = 'item_keep_flag',
      real_sample_rate_attr = 'real_sample_rate'
    )
    ```
    """
    self._add_processor(MerchantFrequencySamplingEnricher(kwargs))
    return self

  def merchant_profile_response_enricher(self , **kwargs):
    """
    MerchantProfileResponseEnricher
    ------
    merchant profile接入

    参数配置
    ------    
    `recv_common_attrs`: [list] 选填项，接收的 common attr 列表，默认不接受 common attr，支持对 attr 重命名保存。

    调用示例
    ------
    ``` python
    .merchant_profile_response_enricher(
      recv_common_attrs=["did"],
    )
    ```
    """
    self._add_processor(MerchantProfileResponseEnricher(kwargs))
    return self
  
  def merchant_common_gsu_feature_enricher(self, **kwargs):
    """
    MerchantCommonGsuFeatureEnricher
    ------
    基于attr进行hard-search

    参数配置
    ------
    `limit_num`: [int] 返回的TopK, 默认50

    `target_attr`: [string] item侧待检索的attr

    `common_action_gsu_attr_list`: [string] common侧待检索的attr
    
    `common_action_category_list`: [string] common侧类目attr

    `common_action_iid_list`: [string] common侧商品id attr
    
    `common_action_aid_list`: [string] common侧aid attr

    `common_action_timestamp_list`: [string] common侧行为时间戳attr

    `common_action_price_list`: [string] common侧price attr

    `gsu_match_iid_list`: [string] 输出gsu_match_iid_list

    `gsu_match_aid_list`: [string] 输出gsu_match_aid_list

    `gsu_match_cid1_list`: [string] 输出gsu_match_cid1_list

    `gsu_match_cid2_list`: [string] 输出gsu_match_cid2_list

    `gsu_match_cid3_list`: [string] 输出gsu_match_cid3_list

    `gsu_match_price_list`: [string] 输出gsu_match_price_list

    `gsu_match_lag_list`: [string] 输出gsu_match_lag_list

    `gsu_match_rank_list`: [string] 输出gsu_match_rank_list

    调用示例
    ------
    ``` python
    .merchant_common_gsu_feature_enricher(
      limit_num = 50,
      target_attr = 'aId',
      common_action_gsu_attr_list = 'common_action_gsu_attr_list',
      common_action_category_list = 'common_action_category_list',
      common_action_iid_list = 'common_action_iid_list',
      common_action_aid_list = 'common_action_aid_list',
      common_action_timestamp_list = 'common_action_timestamp_list',
      common_action_price_list = 'common_action_price_list',
    )
    ```
    """
    self._add_processor(MerchantCommonGsuFeatureEnricher(kwargs))
    return self


  def merchant_goods_slide_beam_search_enricher(self, **kwargs):
        """
        MerchantGoodsSlideBeamSearchEnricher
        ------
        快手货架商品内流 BeamSearch

        参数
        ------
        `return_item_type` : [string] 选配项 序列的item_type,默认为1
        `sequence_max_size`: [int] 选配项 序列长度, 默认6
        `max_sequence_num` : [string] 种子序列个数，默认50
        示例
        ------
        ``` python
        merchant_goods_slide_beam_search_enricher(
           return_item_type=1,
           sequence_max_size=6,
           max_sequence_num=100
        )
        ```
        """
        self._add_processor(
            MerchantGoodsSlideBeamSearchEnricher(kwargs)
        )
        return self
  def merchant_feature_diff_online_offline(self, **kwargs):
        """
        MerchantFeatureDiffOnlineOfflineEnricher
        ------
        电商离在线参数diff算子

        参数
        ------
        `online_batched_sample` : [string] 输入在线侧的 sample attr 打包的 cofea格式编码特征
        `offline_batched_sample` : [string] 输入离线侧的 sample attr 打包的 cofea格式编码特征
        `get_check_slot_from_kconf` : [bool] 是否依赖kconf的对比白明单
        示例
        ------
        ``` python
        merchant_feature_diff_online_offline(
            online_batched_sample='online_batched_sample',
            offline_batched_sample='batched_sample',
            get_check_slot_from_kconf=False
        ) 
        ```
        """
        self._add_processor(
            MerchantFeatureDiffOnlineOfflineEnricher(kwargs)
        )
        return self
  def get_item_sortset_attr_from_redis(self, **kwargs):
        """
        SortsetRedisItemAttrEnricher
        ------
        用 string item attr 为 key 读取 redis value 写入指定的 item_attr 中。
        目前只用于读取sortset格式的直播正在讲解top1最相似的商品

        参数配置
        ------
        `cluster_name`: [string] redis 的 cluster name

        `timeout_ms`: [int] 获取 redis client 的超时时间，默认为 10

        参数配置（方式一：单批获取）
        ------
        `redis_key_from`: [string] 从 string 类型的 item_attr 获取 key

        `save_value_to`: [string] 将 redis 获取到的值存入指定的 string 类型的 item_attr 中

        `key_prefix`: [string][动态参数] 选配项，每个 redis key 的值添加一个前缀，默认为空

        参数配置（各方式共有）
        ------
        调用示例
        ------
        ``` python
        .get_item_sortset_attr_from_redis(
          cluster_name = "redis_cluster_name",
          redis_key_from="redis_key",
          save_value_to="redis_value",
        )
        ```
        """
        self._add_processor(SortsetRedisItemAttrEnricher(kwargs))
        return self
